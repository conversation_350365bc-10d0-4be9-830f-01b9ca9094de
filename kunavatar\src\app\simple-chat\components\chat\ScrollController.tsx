'use client';

import React, { useRef, useEffect, useState, useCallback } from 'react';
import { ChevronDown, ChevronUp } from 'lucide-react';

interface ScrollControllerProps {
  messages: any[];
  isStreaming: boolean;
  children: React.ReactNode;
}

export function ScrollController({ messages, isStreaming, children }: ScrollControllerProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [isNearBottom, setIsNearBottom] = useState(true);
  const [isNearTop, setIsNearTop] = useState(true);
  const [showScrollButtons, setShowScrollButtons] = useState(false);
  const [messageCount, setMessageCount] = useState(messages.length);
  const [userScrolled, setUserScrolled] = useState(false);
  const [lastScrollTime, setLastScrollTime] = useState(0);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 检查滚动位置 - 寻找真正的滚动容器
  const checkScrollPosition = useCallback(() => {
    // 先尝试当前组件的滚动容器
    let container: HTMLElement | null = scrollContainerRef.current;
    
    // 如果当前容器没有滚动条，查找父级的滚动容器
    if (container && container.scrollHeight <= container.clientHeight) {
      // 查找最近的可滚动父元素
      let parent = container.parentElement;
      while (parent) {
        if (parent.scrollHeight > parent.clientHeight && 
            getComputedStyle(parent).overflowY !== 'visible') {
          container = parent;
          break;
        }
        parent = parent.parentElement;
      }
    }
    
    if (!container) return { nearBottom: true, nearTop: true };

    const { scrollTop, scrollHeight, clientHeight } = container;
    const distanceFromBottom = scrollHeight - (scrollTop + clientHeight);
    const distanceFromTop = scrollTop;
    
    // 检测是否接近顶部和底部 - 考虑段落间距影响
    // space-y-4 = 16px，加上padding和其他间距，使用更宽松的阈值
    const nearBottom = distanceFromBottom <= 50; // 放宽底部检测，应对段落间距
    const nearTop = distanceFromTop <= 50;
    
    // 智能显示按钮：当有足够内容可以滚动时就显示
    const hasEnoughContentToScroll = scrollHeight > clientHeight + 100; // 内容高度超过容器高度100px以上
    const showButtons = messages.length > 0 && hasEnoughContentToScroll;
    
    setIsNearBottom(nearBottom);
    setIsNearTop(nearTop);
    setShowScrollButtons(showButtons);
    
    return { nearBottom, nearTop };
  }, [messages.length]);

  // 滚动到底部
  const scrollToBottom = useCallback((force = false) => {
    if (!messagesEndRef.current) return;
    
    const now = Date.now();
    
    // 如果用户最近手动滚动过，且不是强制滚动，则不自动滚动
    if (!force && userScrolled && (now - lastScrollTime < 3000)) {
      return;
    }
    
    try {
      messagesEndRef.current.scrollIntoView({ 
        behavior: force ? 'auto' : 'smooth',
        block: 'end'
      });
    } catch (error) {
      // 降级处理：直接设置scrollTop
      const container = scrollContainerRef.current?.parentElement;
      if (container) {
        container.scrollTop = container.scrollHeight;
      }
    }
  }, [userScrolled, lastScrollTime]);

  // 滚动到顶部
  const scrollToTop = useCallback(() => {
    const container = scrollContainerRef.current?.parentElement;
    if (container) {
      container.scrollTo({ top: 0, behavior: 'smooth' });
    }
  }, []);

  // 处理滚动事件
  const handleScroll = useCallback(() => {
    const now = Date.now();
    setLastScrollTime(now);
    setUserScrolled(true);
    
    // 清除之前的定时器
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }
    
    // 设置新的定时器，3秒后重置用户滚动状态
    scrollTimeoutRef.current = setTimeout(() => {
      setUserScrolled(false);
    }, 3000);
    
    checkScrollPosition();
  }, [checkScrollPosition]);

  // 监听滚动事件
  useEffect(() => {
    const container = scrollContainerRef.current?.parentElement;
    if (container) {
      container.addEventListener('scroll', handleScroll, { passive: true });
      return () => {
        container.removeEventListener('scroll', handleScroll);
      };
    }
  }, [handleScroll]);

  // 当消息数量变化时，检查是否需要自动滚动
  useEffect(() => {
    const hasNewMessages = messages.length > messageCount;
    setMessageCount(messages.length);
    
    if (hasNewMessages) {
      // 延迟检查滚动位置，确保DOM已更新
      setTimeout(() => {
        const { nearBottom } = checkScrollPosition();
        
        // 如果用户在底部附近，或者正在流式输出，自动滚动到底部
        if (nearBottom || isStreaming) {
          scrollToBottom();
        }
      }, 50);
    }
  }, [messages.length, messageCount, isStreaming, checkScrollPosition, scrollToBottom]);

  // 流式输出时自动滚动
  useEffect(() => {
    if (isStreaming && isNearBottom) {
      const interval = setInterval(() => {
        scrollToBottom();
      }, 100);
      
      return () => clearInterval(interval);
    }
  }, [isStreaming, isNearBottom, scrollToBottom]);

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  return (
    <div className="relative">
      <div 
        ref={scrollContainerRef}
        className="p-4 space-y-4"
      >
        {children}
        <div ref={messagesEndRef} />
      </div>
      
      {/* 滚动按钮 */}
      {showScrollButtons && (
        <div className="absolute right-4 bottom-4 flex flex-col gap-2 z-10">
          {!isNearTop && (
            <button
              onClick={scrollToTop}
              className="p-2 bg-theme-background border border-theme-border rounded-full shadow-lg hover:bg-theme-background-secondary transition-colors duration-200"
              title="滚动到顶部"
            >
              <ChevronUp className="w-4 h-4 text-theme-foreground" />
            </button>
          )}
          {!isNearBottom && (
            <button
              onClick={() => scrollToBottom(true)}
              className="p-2 bg-theme-background border border-theme-border rounded-full shadow-lg hover:bg-theme-background-secondary transition-colors duration-200"
              title="滚动到底部"
            >
              <ChevronDown className="w-4 h-4 text-theme-foreground" />
            </button>
          )}
        </div>
      )}
    </div>
  );
}

/**
 * 用户设置数据库迁移脚本
 * 创建 user_settings 表并迁移现有数据
 */

const Database = require('better-sqlite3');
const path = require('path');

// 数据库文件路径
const dbPath = path.join(__dirname, '..', 'chat.db');
const db = new Database(dbPath);

console.log('开始用户设置数据库迁移...');

try {
  // 开始事务
  db.exec('BEGIN TRANSACTION');

  // 1. 创建 user_settings 表
  console.log('创建 user_settings 表...');
  db.exec(`
    CREATE TABLE IF NOT EXISTS user_settings (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      
      -- 界面设置
      theme TEXT DEFAULT 'light',
      chat_style TEXT DEFAULT 'conversation',
      language TEXT DEFAULT 'zh-CN',
      
      -- 辅助模型设置
      default_model TEXT,
      title_summary_enabled BOOLEAN DEFAULT 1,
      title_summary_model TEXT,
      title_summary_prompt TEXT,
      prompt_optimize_enabled BOOLEAN DEFAULT 1,
      prompt_optimize_model TEXT,
      prompt_optimize_prompt TEXT,
      memory_enabled BOOLEAN DEFAULT 1,
      memory_model TEXT,
      
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      
      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
      UNIQUE(user_id)
    )
  `);

  // 2. 创建索引
  console.log('创建索引...');
  db.exec(`
    CREATE INDEX IF NOT EXISTS idx_user_settings_user_id ON user_settings(user_id);
  `);

  // 3. 添加 user_id 字段到现有表（如果不存在）
  console.log('检查并添加 user_id 字段到现有表...');
  
  // 检查 conversations 表是否有 user_id 字段
  const conversationsColumns = db.prepare("PRAGMA table_info(conversations)").all();
  const hasConversationUserId = conversationsColumns.some(col => col.name === 'user_id');
  
  if (!hasConversationUserId) {
    console.log('添加 user_id 到 conversations 表...');
    db.exec('ALTER TABLE conversations ADD COLUMN user_id INTEGER DEFAULT 1');
    db.exec('CREATE INDEX IF NOT EXISTS idx_conversations_user_id ON conversations(user_id)');
  }

  // 检查 messages 表是否有 user_id 字段
  const messagesColumns = db.prepare("PRAGMA table_info(messages)").all();
  const hasMessageUserId = messagesColumns.some(col => col.name === 'user_id');
  
  if (!hasMessageUserId) {
    console.log('添加 user_id 到 messages 表...');
    db.exec('ALTER TABLE messages ADD COLUMN user_id INTEGER DEFAULT 1');
    db.exec('CREATE INDEX IF NOT EXISTS idx_messages_user_id ON messages(user_id)');
  }

  // 检查 agents 表是否有 user_id 字段
  const agentsColumns = db.prepare("PRAGMA table_info(agents)").all();
  const hasAgentUserId = agentsColumns.some(col => col.name === 'user_id');
  
  if (!hasAgentUserId) {
    console.log('添加 user_id 到 agents 表...');
    db.exec('ALTER TABLE agents ADD COLUMN user_id INTEGER DEFAULT 1');
    db.exec('CREATE INDEX IF NOT EXISTS idx_agents_user_id ON agents(user_id)');
  }

  // 检查 custom_models 表是否有 user_id 字段
  const customModelsColumns = db.prepare("PRAGMA table_info(custom_models)").all();
  const hasCustomModelUserId = customModelsColumns.some(col => col.name === 'user_id');
  
  if (!hasCustomModelUserId) {
    console.log('添加 user_id 到 custom_models 表...');
    db.exec('ALTER TABLE custom_models ADD COLUMN user_id INTEGER DEFAULT 1');
    db.exec('CREATE INDEX IF NOT EXISTS idx_custom_models_user_id ON custom_models(user_id)');
  }

  // 4. 为现有用户创建默认设置
  console.log('为现有用户创建默认设置...');
  const users = db.prepare('SELECT id FROM users').all();
  
  for (const user of users) {
    const existingSetting = db.prepare('SELECT id FROM user_settings WHERE user_id = ?').get(user.id);
    
    if (!existingSetting) {
      db.prepare(`
        INSERT INTO user_settings (
          user_id, theme, chat_style, language,
          title_summary_enabled, prompt_optimize_enabled, memory_enabled
        ) VALUES (?, 'light', 'conversation', 'zh-CN', 1, 1, 1)
      `).run(user.id);
      console.log(`为用户 ${user.id} 创建默认设置`);
    }
  }

  // 5. 创建更新触发器
  console.log('创建更新触发器...');
  db.exec(`
    CREATE TRIGGER IF NOT EXISTS update_user_settings_timestamp
    AFTER UPDATE ON user_settings
    FOR EACH ROW
    BEGIN
      UPDATE user_settings SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;
  `);

  // 提交事务
  db.exec('COMMIT');
  console.log('用户设置数据库迁移完成！');

} catch (error) {
  // 回滚事务
  db.exec('ROLLBACK');
  console.error('迁移失败，已回滚:', error);
  process.exit(1);
} finally {
  db.close();
}
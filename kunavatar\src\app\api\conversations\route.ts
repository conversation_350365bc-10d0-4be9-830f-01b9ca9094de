import { dbOperations } from '../../../lib/database';
import { withAuth } from '../../../lib/middleware/auth';
import { ApiRouteErrorHandler } from '../../../lib/utils/apiRouteErrorHandler';
import { ValidationUtils } from '../../../lib/utils/validationUtils';
import { DbUtils } from '../../../lib/utils/dbUtils';

// 获取用户的所有对话
export const GET = withAuth(ApiRouteErrorHandler.wrapHandler(async (request) => {
  const userId = request.user!.id;

  // 获取用户对话列表（带统计信息）
  const result = DbUtils.getUserConversationsWithStats(userId);
  if (!result.success) {
    throw new Error(result.error);
  }

  return ApiRouteErrorHandler.createSuccessResponse({
    conversations: result.conversations
  });
}, '获取对话列表'));

// 创建新对话
export const POST = withAuth(ApiRouteErrorHandler.wrapHandler(async (request) => {
  const userId = request.user!.id;
  const body = await request.json();
  const { title, model }: { title: string; model: string } = body;

  // 验证必需参数
  const validation = ApiRouteErrorHandler.validateRequiredParams(
    { title, model },
    ['title', 'model']
  );
  if (!validation.isValid) {
    return validation.error!;
  }

  // 验证标题格式
  const titleValidation = ValidationUtils.validateTitle(title);
  if (!titleValidation.isValid) {
    return ApiRouteErrorHandler.handleValidationError(titleValidation.error!);
  }

  // 验证模型名称
  const modelValidation = ValidationUtils.validateModelName(model);
  if (!modelValidation.isValid) {
    return ApiRouteErrorHandler.handleValidationError(modelValidation.error!);
  }

  // 创建对话
  const result = DbUtils.createConversation(title, model, userId);
  if (!result.success) {
    throw new Error(result.error);
  }

  // 获取创建的对话
  const conversation = dbOperations.getConversationById(result.conversationId!);

  return ApiRouteErrorHandler.createSuccessResponse({
    conversation
  }, 201);
}, '创建对话'));
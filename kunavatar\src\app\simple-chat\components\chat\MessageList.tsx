'use client';

import React from 'react';
import { ScrollController } from './ScrollController';
import { MessageRenderer } from './MessageRenderer';
import { ChatStyle } from '../input-controls';
import { SimpleMessage } from '../../types';
import { AgentWithRelations } from '@/app/agents/types';

interface MessageListProps {
  messages: SimpleMessage[];
  isStreaming: boolean;
  expandedThinkingMessages: Set<string>;
  onToggleThinkingExpand: (messageId: string) => void;
  chatStyle: ChatStyle;
  selectedModel?: string;
  customModels?: Array<{
    base_model: string;
    display_name: string;
    family?: string;
  }>;
  selectedAgent?: AgentWithRelations | null;
}

export function MessageList({
  messages,
  isStreaming,
  expandedThinkingMessages,
  onToggleThinkingExpand,
  chatStyle,
  selectedModel,
  customModels,
  selectedAgent,
}: MessageListProps) {
  return (
    <ScrollController messages={messages} isStreaming={isStreaming}>
      <MessageRenderer
        messages={messages}
        isStreaming={isStreaming}
        expandedThinkingMessages={expandedThinkingMessages}
        onToggleThinkingExpand={onToggleThinkingExpand}
        chatStyle={chatStyle}
        selectedModel={selectedModel}
        customModels={customModels}
        selectedAgent={selectedAgent}
      />
    </ScrollController>
  );
}
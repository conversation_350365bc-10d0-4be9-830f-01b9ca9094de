'use client';

import React, { Suspense } from 'react';
import { PageLoading } from '../../components/Loading';
import { ChatPageContainer } from './components/ChatPageContainer';

// 内部组件，使用useSearchParams
function SimpleChatPageContent() {
  return <ChatPageContainer />;
}

// 外部组件，用Suspense包装内部组件
export default function SimpleChatPage() {
  return (
    <Suspense fallback={
      <div className="flex h-screen bg-background">
        <PageLoading
          text="loading"
          fullScreen={true}
        />
      </div>
    }>
      <SimpleChatPageContent />
    </Suspense>
  );
}
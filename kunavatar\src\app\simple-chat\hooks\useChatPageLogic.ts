'use client';

import { useCallback } from 'react';
import { CustomModel } from '@/lib/database/custom-models';
import { Conversation } from '@/lib/database';
import { MessageTransformer } from '../utils/messageTransformer';
import { ApiClient } from '../utils/apiClient';

interface UseChatPageLogicProps {
  models: CustomModel[];
  selectedModel: string;
  currentConversation: Conversation | null;
  conversationLoading: boolean;
  createConversation: (title: string, model: string) => Promise<number | null>;
  switchConversation: (conversationId: number) => Promise<void>;
  setSelectedModel: (model: string, conversationId?: number) => void;
  setMessages: (messages: any[]) => void;
  setToolCalls: (toolCalls: any[]) => void;
  selectBestModel: (
    availableModels: CustomModel[],
    conversationId?: number,
    lastUsedModel?: string,
    conversationModel?: string
  ) => void;
}

export function useChatPageLogic({
  models,
  selectedModel,
  currentConversation,
  conversationLoading,
  createConversation,
  switchConversation,
  setSelectedModel,
  setMessages,
  setToolCalls,
  selectBestModel,
}: UseChatPageLogicProps) {
  
  // 从数据库数据更新消息的辅助函数
  const updateMessagesFromDatabase = useCallback((dbMessages: any[]) => {
    const { messages, toolCalls } = MessageTransformer.fromDatabase(dbMessages);
    setMessages(messages);
    setToolCalls(toolCalls);
  }, [setMessages, setToolCalls]);

  // 清空当前对话
  const clearCurrentChat = useCallback(async () => {
    if (!currentConversation) return;

    try {
      await ApiClient.clearConversation(currentConversation.id);

      // 清空当前消息
      setMessages([]);
      setToolCalls([]);
    } catch (error) {
      console.error('清空对话失败:', error);
      throw error;
    }
  }, [currentConversation, setMessages, setToolCalls]);

  // 插入文本到输入框
  const handleInsertText = useCallback((text: string, setInputMessage: (text: string) => void) => {
    setInputMessage(text);
  }, []);

  return {
    updateMessagesFromDatabase,
    clearCurrentChat,
    handleInsertText,
  };
}

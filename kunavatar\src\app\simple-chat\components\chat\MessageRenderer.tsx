'use client';

import React from 'react';
import { MessageItem } from './MessageItem';
import { ChatStyle } from '../input-controls';
import { SimpleMessage } from '../../types';
import { AgentWithRelations } from '@/app/agents/types';

interface MessageRendererProps {
  messages: SimpleMessage[];
  isStreaming: boolean;
  expandedThinkingMessages: Set<string>;
  onToggleThinkingExpand: (messageId: string) => void;
  chatStyle: ChatStyle;
  selectedModel?: string;
  customModels?: Array<{
    base_model: string;
    display_name: string;
    family?: string;
  }>;
  selectedAgent?: AgentWithRelations | null;
}

export function MessageRenderer({
  messages,
  isStreaming,
  expandedThinkingMessages,
  onToggleThinkingExpand,
  chatStyle,
  selectedModel,
  customModels,
  selectedAgent,
}: MessageRendererProps) {
  
  if (messages.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center text-theme-foreground-muted">
          <div className="text-lg mb-2">👋</div>
          <p>开始新的对话吧！</p>
          <p className="text-sm mt-1">输入消息开始聊天</p>
        </div>
      </div>
    );
  }

  return (
    <>
      {messages.map((message, index) => (
        <MessageItem
          key={message.id}
          message={message}
          index={index}
          isStreaming={isStreaming}
          isLastMessage={index === messages.length - 1}
          expandedThinkingMessages={expandedThinkingMessages}
          onToggleThinkingExpand={onToggleThinkingExpand}
          chatStyle={chatStyle}
          selectedModel={selectedModel}
          customModels={customModels}
          selectedAgent={selectedAgent}
        />
      ))}
    </>
  );
}

'use client';

import React, { useState, useEffect } from 'react';
import { Sidebar } from '../Sidebar';
import { Conversation } from '@/lib/database';

interface SidebarWrapperProps {
  onCreateConversation?: () => void;
  onLoadConversation?: (conversationId: number) => void;
  onDeleteConversation?: (conversationId: number) => void;
}

export function SidebarWrapper({
  onCreateConversation,
  onLoadConversation,
  onDeleteConversation,
}: SidebarWrapperProps) {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [loading, setLoading] = useState(false);

  // 加载对话列表
  const loadConversations = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('accessToken');
      const response = await fetch('/api/conversations', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });
      const data = await response.json();
      
      if (data.success) {
        setConversations(data.conversations || []);
      }
    } catch (err) {
      console.error('加载对话列表失败:', err);
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载时加载对话列表
  useEffect(() => {
    loadConversations();
  }, []);

  // 默认的事件处理函数
  const handleCreateConversation = onCreateConversation || (() => {
    window.location.href = '/simple-chat?new=true';
  });

  const handleLoadConversation = onLoadConversation || ((conversationId: number) => {
    window.location.href = `/simple-chat?id=${conversationId}`;
  });

  const handleDeleteConversation = onDeleteConversation || (async (conversationId: number) => {
    // 默认实现：删除对话并刷新列表
    try {
      const token = localStorage.getItem('accessToken');
      const response = await fetch(`/api/conversations/${conversationId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });
      
      if (response.ok) {
        // 刷新对话列表
        await loadConversations();
      }
    } catch (error) {
      console.error('删除对话失败:', error);
    }
  });

  return (
    <Sidebar
      conversations={conversations}
      currentConversation={null}
      onCreateConversation={handleCreateConversation}
      onLoadConversation={handleLoadConversation}
      onDeleteConversation={handleDeleteConversation}
    />
  );
}

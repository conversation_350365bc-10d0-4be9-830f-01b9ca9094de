import { ChatMessage } from '../../../../lib/ollama';
import { MemoryService } from './memoryService';

/**
 * 记忆上下文注入器 - 处理记忆信息的注入逻辑
 */
export class MemoryContextInjector {
  /**
   * 注入记忆上下文到消息中
   */
  static async injectMemoryContext(
    messages: ChatMessage[],
    conversationId: number | undefined,
    userId: string
  ): Promise<ChatMessage[]> {
    if (!conversationId) {
      return messages;
    }
    
    try {
      // 获取记忆上下文
      const memoryContext = MemoryService.getMemoryContext(conversationId, parseInt(userId));
      
      if (!memoryContext || memoryContext.trim().length === 0) {
        return messages;
      }
      
      // 注入记忆上下文
      return MemoryContextInjector.injectContext(messages, memoryContext);
      
    } catch (error) {
      console.error('获取记忆上下文时出错:', error);
      return messages;
    }
  }

  /**
   * 将记忆上下文注入到消息列表中
   */
  static injectContext(messages: ChatMessage[], memoryContext: string): ChatMessage[] {
    const systemMessageIndex = messages.findIndex(msg => msg.role === 'system');
    
    if (systemMessageIndex >= 0) {
      // 如果存在系统消息，将记忆上下文以结构化方式添加到系统消息中
      const existingSystemMessage = messages[systemMessageIndex];
      const enhancedSystemMessage = {
        ...existingSystemMessage,
        content: `${existingSystemMessage.content}\n\n--- 历史记忆信息 ---\n${memoryContext.trim()}\n--- 记忆信息结束 ---\n\n请参考以上记忆信息来回答用户问题，保持你的角色特性和指令不变。`
      };
      
      const updatedMessages = [...messages];
      updatedMessages[systemMessageIndex] = enhancedSystemMessage;
      
      console.log('✅ 记忆上下文已注入到现有系统消息中');
      return updatedMessages;
    } else {
      // 如果不存在系统消息，创建一个新的系统消息包含记忆上下文
      const memorySystemMessage: ChatMessage = {
        role: 'system',
        content: `--- 历史记忆信息 ---\n${memoryContext.trim()}\n--- 记忆信息结束 ---\n\n请参考以上记忆信息来回答用户问题。`
      };
      
      console.log('✅ 记忆上下文已作为新系统消息添加');
      return [memorySystemMessage, ...messages];
    }
  }

  /**
   * 检查消息是否已包含记忆上下文
   */
  static hasMemoryContext(messages: ChatMessage[]): boolean {
    const systemMessage = messages.find(msg => msg.role === 'system');
    return systemMessage?.content?.includes('--- 历史记忆信息 ---') || false;
  }

  /**
   * 从消息中移除记忆上下文
   */
  static removeMemoryContext(messages: ChatMessage[]): ChatMessage[] {
    return messages.map(message => {
      if (message.role === 'system' && message.content?.includes('--- 历史记忆信息 ---')) {
        // 移除记忆上下文部分
        const content = message.content.replace(
          /--- 历史记忆信息 ---[\s\S]*?--- 记忆信息结束 ---\n\n请参考以上记忆信息来回答用户问题[，。]?[^。]*。?\n*/g,
          ''
        ).trim();
        
        // 如果移除记忆上下文后系统消息为空，则移除整个系统消息
        if (!content) {
          return null;
        }
        
        return {
          ...message,
          content
        };
      }
      return message;
    }).filter(Boolean) as ChatMessage[];
  }

  /**
   * 提取记忆上下文内容
   */
  static extractMemoryContext(messages: ChatMessage[]): string | null {
    const systemMessage = messages.find(msg => msg.role === 'system');
    
    if (!systemMessage?.content) {
      return null;
    }
    
    const match = systemMessage.content.match(
      /--- 历史记忆信息 ---\n([\s\S]*?)\n--- 记忆信息结束 ---/
    );
    
    return match ? match[1].trim() : null;
  }

  /**
   * 更新消息中的记忆上下文
   */
  static async updateMemoryContext(
    messages: ChatMessage[],
    conversationId: number | undefined,
    userId: string
  ): Promise<ChatMessage[]> {
    if (!conversationId) {
      return messages;
    }
    
    try {
      // 先移除现有的记忆上下文
      const messagesWithoutMemory = MemoryContextInjector.removeMemoryContext(messages);
      
      // 重新注入最新的记忆上下文
      return await MemoryContextInjector.injectMemoryContext(
        messagesWithoutMemory,
        conversationId,
        userId
      );
      
    } catch (error) {
      console.error('更新记忆上下文时出错:', error);
      return messages;
    }
  }

  /**
   * 验证记忆上下文的有效性
   */
  static validateMemoryContext(memoryContext: string): boolean {
    if (!memoryContext || typeof memoryContext !== 'string') {
      return false;
    }
    
    // 检查长度限制（避免上下文过长）
    const maxLength = 10000; // 10KB
    if (memoryContext.length > maxLength) {
      console.warn('记忆上下文过长，可能影响性能');
      return false;
    }
    
    // 检查是否包含有效内容
    const trimmedContent = memoryContext.trim();
    if (trimmedContent.length === 0) {
      return false;
    }
    
    return true;
  }

  /**
   * 格式化记忆上下文
   */
  static formatMemoryContext(memoryContext: string): string {
    if (!memoryContext) {
      return '';
    }
    
    // 清理多余的空白字符
    let formatted = memoryContext.trim();
    
    // 确保段落之间有适当的间距
    formatted = formatted.replace(/\n{3,}/g, '\n\n');
    
    // 移除行首行尾的空白
    formatted = formatted.split('\n').map(line => line.trim()).join('\n');
    
    return formatted;
  }
}

{"mcpServers": {"Exa Search": {"type": "streamable-http", "url": "https://server.smithery.ai/exa/mcp?api_key=f249a372-7b6e-4b96-9e5a-050c320e8b18&profile=close-aphid-tzkXG3", "path": "/", "protocol": "http"}, "Context7": {"type": "streamable-http", "url": "https://server.smithery.ai/@upstash/context7-mcp/mcp?api_key=f249a372-7b6e-4b96-9e5a-050c320e8b18", "path": "/", "protocol": "http", "description": "将特定于版本的最新文档和代码示例直接提取到您的提示中。通过消除过时信息和幻觉 API 来增强您的编码体验。只需将 use context7 添加到您的问题中，即可获得准确且相关的答案。"}, "": {"type": "sse", "url": "https://mcp.api-inference.modelscope.net/31679cda84794e/sse", "path": "/", "protocol": "http"}, "Tavily智搜": {"type": "sse", "url": "https://mcp.api-inference.modelscope.net/3cce9c7844f042/sse", "path": "/", "protocol": "http", "description": "该服务器使AI系统能够与Tavily的搜索和数据提取工具集成，提供实时的网络信息访问和领域特定的搜索。"}, "时间服务": {"type": "sse", "url": "https://mcp.api-inference.modelscope.net/21c27a72682940/sse", "path": "/", "protocol": "http", "description": "一个提供时间和时区转换功能的模型上下文协议服务器。该服务器使大型语言模型能够获取当前时间信息，并使用IANA时区名称进行时区转换，同时具备自动检测系统时区的功能。"}, "Time MCP Server": {"type": "streamable-http", "url": "https://server.smithery.ai/@yokingma/time-mcp/mcp?api_key=f249a372-7b6e-4b96-9e5a-050c320e8b18&profile=close-aphid-tzkXG3", "path": "/", "protocol": "http", "description": "Empower your LLMs with time awareness capabilities. Access current time, convert between timezones, and get timestamps effortlessly. "}, "Weather MCP Server": {"type": "streamable-http", "url": "https://server.smithery.ai/@isdaniel/mcp_weather_server/mcp?api_key=f249a372-7b6e-4b96-9e5a-050c320e8b18&profile=close-aphid-tzkXG3", "path": "/", "protocol": "http", "description": "Retrieve real-time weather information effortlessly for any city. Get accurate weather updates using a simple command or API call without needing an API key. Enhance your applications with reliable weather data from the Open-Meteo API."}, "Desktop Commander": {"type": "streamable-http", "url": "https://server.smithery.ai/@wonderwhy-er/desktop-commander/mcp?api_key=f249a372-7b6e-4b96-9e5a-050c320e8b18&profile=close-aphid-tzkXG3", "path": "/", "protocol": "http"}, "高德地图": {"type": "sse", "url": "https://mcp.api-inference.modelscope.net/cf9eb51cc53141/sse", "path": "/", "protocol": "http", "description": "高德地图是一个支持任何MCP协议客户端的服务器，允许用户轻松利用高德地图MCP服务器获取各种基于位置的服务。"}}}
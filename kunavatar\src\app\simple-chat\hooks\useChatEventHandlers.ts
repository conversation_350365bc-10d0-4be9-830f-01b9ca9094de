'use client';

import { useCallback } from 'react';
import { Conversation } from '@/lib/database';
import { ApiClient } from '../utils/apiClient';
import { ErrorHandler } from '../utils/errorHandler';

interface UseChatEventHandlersProps {
  currentConversation: Conversation | null;
  conversations: Conversation[];
  selectedModel: string;
  createConversation: (title: string, model: string) => Promise<number | null>;
  switchConversation: (conversationId: number) => Promise<void>;
  deleteConversation: (conversationId: number) => Promise<void>;
  loadConversations: () => Promise<void>;
  setMessages: (messages: any[]) => void;
  setToolCalls: (toolCalls: any[]) => void;
  setSelectedModel: (model: string, conversationId?: number) => void;
  setError: (error: string | null) => void;
}

export function useChatEventHandlers({
  currentConversation,
  conversations,
  selectedModel,
  createConversation,
  switchConversation,
  deleteConversation,
  loadConversations,
  setMessages,
  setToolCalls,
  setSelectedModel,
  setError,
}: UseChatEventHandlersProps) {

  // 侧边栏事件处理函数 - 优化：按需加载对话列表
  const handleCreateConversation = useCallback(() => {
    if (typeof window !== 'undefined') {
      window.location.href = '/simple-chat?new=true';
    }
  }, []);

  const handleLoadConversation = useCallback(async (conversationId: number) => {
    // 确保有对话列表数据
    await loadConversations();
    if (typeof window !== 'undefined') {
      window.location.href = `/simple-chat?id=${conversationId}`;
    }
  }, [loadConversations]);

  const handleDeleteConversation = useCallback(async (conversationId: number) => {
    try {
      // 确保有对话列表数据
      await loadConversations();

      await ApiClient.deleteConversation(conversationId);
      await loadConversations(); // 删除后刷新列表

      // 如果删除的是当前对话，重定向到新对话
      if (currentConversation?.id === conversationId) {
        if (typeof window !== 'undefined') {
          window.location.href = '/simple-chat?new=true';
        }
      }
    } catch (error) {
      console.error('Failed to delete conversation:', error);
      setError(ErrorHandler.createUserFriendlyMessage(error));
    }
  }, [loadConversations, currentConversation, setError]);

  // 清空当前对话
  const clearCurrentChat = useCallback(async () => {
    if (!currentConversation) return;

    try {
      await ApiClient.clearConversation(currentConversation.id);

      // 清空当前消息
      setMessages([]);
      setToolCalls([]);
      setError(null);

      // 重新加载对话列表
      loadConversations();
    } catch (error) {
      console.error('清空对话失败:', error);
      setError(ErrorHandler.createUserFriendlyMessage(error));
    }
  }, [currentConversation, setMessages, setToolCalls, setError, loadConversations]);

  return {
    handleCreateConversation,
    handleLoadConversation,
    handleDeleteConversation,
    clearCurrentChat,
  };
}

import { NextResponse } from 'next/server';
import { z } from 'zod';

/**
 * API路由统一错误处理工具类
 */
export class ApiRouteErrorHandler {
  /**
   * 处理API路由错误并返回标准响应
   */
  static handleError(error: unknown, context?: string): NextResponse {
    console.error(`API错误${context ? ` (${context})` : ''}:`, error);
    
    // Zod验证错误
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: '输入数据验证失败',
        details: error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message,
        })),
      }, { status: 400 });
    }
    
    // 权限错误
    if (error instanceof Error && (
      error.message.includes('unauthorized') ||
      error.message.includes('权限不足') ||
      error.message.includes('无权限访问')
    )) {
      return NextResponse.json({
        success: false,
        error: '权限不足',
        message: '请重新登录后重试',
      }, { status: 401 });
    }
    
    // 资源不存在错误
    if (error instanceof Error && (
      error.message.includes('不存在') ||
      error.message.includes('not found')
    )) {
      return NextResponse.json({
        success: false,
        error: '资源不存在',
        message: error.message,
      }, { status: 404 });
    }
    
    // 数据库错误
    if (error instanceof Error && (
      error.message.includes('database') ||
      error.message.includes('SQLITE') ||
      error.message.includes('SQL')
    )) {
      return NextResponse.json({
        success: false,
        error: '数据库操作失败',
        message: '服务器内部错误，请稍后重试',
      }, { status: 500 });
    }
    
    // 服务不可用错误
    if (error instanceof Error && (
      error.message.includes('服务不可用') ||
      error.message.includes('service unavailable') ||
      error.message.includes('Ollama')
    )) {
      return NextResponse.json({
        success: false,
        error: '服务不可用',
        message: error.message,
      }, { status: 503 });
    }
    
    // 通用错误
    if (error instanceof Error) {
      return NextResponse.json({
        success: false,
        error: context ? `${context}失败` : '操作失败',
        message: error.message,
      }, { status: 500 });
    }
    
    // 未知错误
    return NextResponse.json({
      success: false,
      error: '未知错误',
      message: '服务器内部错误',
    }, { status: 500 });
  }

  /**
   * 处理参数验证错误
   */
  static handleValidationError(message: string, field?: string): NextResponse {
    return NextResponse.json({
      success: false,
      error: '参数验证失败',
      message,
      field,
    }, { status: 400 });
  }

  /**
   * 处理权限错误
   */
  static handlePermissionError(message: string = '权限不足'): NextResponse {
    return NextResponse.json({
      success: false,
      error: '权限不足',
      message,
    }, { status: 403 });
  }

  /**
   * 处理资源不存在错误
   */
  static handleNotFoundError(resource: string): NextResponse {
    return NextResponse.json({
      success: false,
      error: '资源不存在',
      message: `${resource}不存在或无权限访问`,
    }, { status: 404 });
  }

  /**
   * 处理服务不可用错误
   */
  static handleServiceUnavailableError(service: string, details?: string): NextResponse {
    return NextResponse.json({
      success: false,
      error: `${service}服务不可用`,
      message: details || `请确保${service}正在运行`,
    }, { status: 503 });
  }

  /**
   * 创建成功响应
   */
  static createSuccessResponse(data: any, status: number = 200): NextResponse {
    return NextResponse.json({
      success: true,
      ...data,
    }, { status });
  }

  /**
   * 包装异步API处理器
   */
  static wrapHandler(
    handler: (...args: any[]) => Promise<NextResponse>,
    context?: string
  ) {
    return async (...args: any[]): Promise<NextResponse> => {
      try {
        return await handler(...args);
      } catch (error) {
        return ApiRouteErrorHandler.handleError(error, context);
      }
    };
  }

  /**
   * 验证必需参数
   */
  static validateRequiredParams(
    params: Record<string, any>,
    requiredFields: string[]
  ): { isValid: boolean; error?: NextResponse } {
    for (const field of requiredFields) {
      if (!params[field]) {
        return {
          isValid: false,
          error: ApiRouteErrorHandler.handleValidationError(
            `缺少必需参数: ${field}`,
            field
          ),
        };
      }
    }
    return { isValid: true };
  }

  /**
   * 验证用户权限
   */
  static validateUserPermission(
    userId: string | number,
    resourceUserId: string | number
  ): { isValid: boolean; error?: NextResponse } {
    if (userId !== resourceUserId) {
      return {
        isValid: false,
        error: ApiRouteErrorHandler.handlePermissionError('无权限访问此资源'),
      };
    }
    return { isValid: true };
  }

  /**
   * 验证ID参数
   */
  static validateId(id: string | undefined, resourceName: string): { 
    isValid: boolean; 
    id?: number; 
    error?: NextResponse 
  } {
    if (!id) {
      return {
        isValid: false,
        error: ApiRouteErrorHandler.handleValidationError(`无效的${resourceName}ID`),
      };
    }

    const numericId = parseInt(id);
    if (isNaN(numericId) || numericId <= 0) {
      return {
        isValid: false,
        error: ApiRouteErrorHandler.handleValidationError(`无效的${resourceName}ID格式`),
      };
    }

    return { isValid: true, id: numericId };
  }
}

import { SimpleMessage } from '../types';

/**
 * 消息转换工具类 - 统一处理消息格式转换
 */
export class MessageTransformer {
  /**
   * 从数据库消息转换为前端消息格式
   */
  static fromDatabase(dbMessages: any[]): { messages: SimpleMessage[]; toolCalls: any[] } {
    console.log('🔧 转换数据库消息，总数:', dbMessages.length);
    
    // 按时间戳和ID排序
    const allMessages = dbMessages.sort((a: any, b: any) => {
      if (a.timestamp !== b.timestamp) {
        return a.timestamp - b.timestamp;
      }
      return a.id - b.id;
    });
    
    const formattedMessages: SimpleMessage[] = [];
    const toolCallMessages: any[] = [];
    
    for (const msg of allMessages) {
      if (msg.role === 'tool_call' && msg.tool_name) {
        // 处理工具调用消息
        const toolCall = MessageTransformer.parseToolCallMessage(msg);
        toolCallMessages.push(toolCall);
        
        // 创建工具调用占位符消息
        formattedMessages.push({
          id: `tool-placeholder-${msg.id}`,
          role: 'tool_call' as any,
          content: '',
          timestamp: msg.timestamp || new Date(msg.created_at).getTime(),
          toolCall: toolCall,
        });
      } else {
        // 处理普通消息
        formattedMessages.push(MessageTransformer.parseRegularMessage(msg));
      }
    }
    
    // 检查是否有统计信息
    const hasStats = formattedMessages.some((msg: any) => 
      msg.role === 'assistant' && (msg.total_duration || msg.eval_count)
    );
    console.log('🔧 转换后的消息是否包含统计信息:', hasStats);
    console.log('🔧 转换后的工具调用数量:', toolCallMessages.length);
    
    return { messages: formattedMessages, toolCalls: toolCallMessages };
  }

  /**
   * 解析工具调用消息
   */
  private static parseToolCallMessage(msg: any): any {
    let args = {};
    let result = '';
    
    try {
      args = msg.tool_args ? JSON.parse(msg.tool_args) : {};
    } catch (e) {
      console.warn('解析工具参数失败:', e);
      args = {};
    }
    
    try {
      result = msg.tool_result ? 
        (typeof msg.tool_result === 'string' ? msg.tool_result : JSON.stringify(msg.tool_result)) 
        : '';
    } catch (e) {
      console.warn('解析工具结果失败:', e);
      result = msg.tool_result || '';
    }
    
    return {
      id: `tool-${msg.id}`,
      toolName: msg.tool_name,
      args: args,
      status: msg.tool_status || 'completed',
      result: result,
      error: msg.tool_error || undefined,
      startTime: msg.timestamp || new Date(msg.created_at).getTime(),
      executionTime: msg.tool_execution_time || 0,
    };
  }

  /**
   * 解析普通消息
   */
  private static parseRegularMessage(msg: any): SimpleMessage {
    return {
      id: `msg-${msg.id}`,
      role: msg.role,
      content: msg.content,
      timestamp: msg.timestamp || new Date(msg.created_at).getTime(),
      model: msg.model,
      // 包含统计字段
      total_duration: msg.total_duration,
      load_duration: msg.load_duration,
      prompt_eval_count: msg.prompt_eval_count,
      prompt_eval_duration: msg.prompt_eval_duration,
      eval_count: msg.eval_count,
      eval_duration: msg.eval_duration,
    };
  }

  /**
   * 转换为API格式
   */
  static toApiFormat(messages: SimpleMessage[]): any[] {
    return messages
      .filter(msg => msg.role !== 'tool_call') // 过滤掉工具调用占位符
      .map(msg => ({
        role: msg.role,
        content: msg.content,
      }));
  }

  /**
   * 提取最后一条用户消息
   */
  static extractLastUserMessage(messages: any[]): { content: string } | null {
    for (let i = messages.length - 1; i >= 0; i--) {
      if (messages[i].role === 'user') {
        return { content: messages[i].content };
      }
    }
    return null;
  }

  /**
   * 验证消息格式
   */
  static validateMessage(message: any): boolean {
    if (!message || typeof message !== 'object') {
      return false;
    }
    
    if (!message.role || !['user', 'assistant', 'system', 'tool', 'tool_call'].includes(message.role)) {
      return false;
    }
    
    if (typeof message.content !== 'string') {
      return false;
    }
    
    return true;
  }

  /**
   * 清理消息内容
   */
  static sanitizeContent(content: string): string {
    if (!content || typeof content !== 'string') {
      return '';
    }
    
    // 移除潜在的恶意脚本
    return content
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '');
  }

  /**
   * 格式化时间戳
   */
  static formatTimestamp(timestamp: number): string {
    return new Date(timestamp).toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  }

  /**
   * 计算消息统计信息
   */
  static calculateStats(messages: SimpleMessage[]): {
    totalMessages: number;
    userMessages: number;
    assistantMessages: number;
    toolCalls: number;
    totalTokens: number;
  } {
    let userMessages = 0;
    let assistantMessages = 0;
    let toolCalls = 0;
    let totalTokens = 0;
    
    for (const message of messages) {
      switch (message.role) {
        case 'user':
          userMessages++;
          break;
        case 'assistant':
          assistantMessages++;
          if (message.eval_count) {
            totalTokens += message.eval_count;
          }
          break;
        case 'tool_call':
          toolCalls++;
          break;
      }
    }
    
    return {
      totalMessages: messages.length,
      userMessages,
      assistantMessages,
      toolCalls,
      totalTokens
    };
  }
}

import { <PERSON>rror<PERSON>and<PERSON> } from './errorHandler';

/**
 * 统一API客户端工具类
 */
export class ApiClient {
  private static baseUrl = '';
  private static defaultHeaders = {
    'Content-Type': 'application/json',
  };

  /**
   * 获取认证头
   */
  private static getAuthHeaders(): Record<string, string> {
    const token = localStorage.getItem('accessToken');
    return token ? { 'Authorization': `Bearer ${token}` } : {};
  }

  /**
   * 通用请求方法
   */
  private static async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    const headers = {
      ...this.defaultHeaders,
      ...this.getAuthHeaders(),
      ...options.headers,
    };

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      ErrorHandler.logError(error, `API请求失败: ${endpoint}`, { options });
      throw error;
    }
  }

  /**
   * GET请求
   */
  static async get<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'GET' });
  }

  /**
   * POST请求
   */
  static async post<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * PUT请求
   */
  static async put<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * DELETE请求
   */
  static async delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }

  /**
   * 发送聊天消息
   */
  static async sendChatMessage(data: {
    model: string;
    conversationId?: number;
    agentId?: number | null;
    messages: any[];
    stream: boolean;
    enableTools: boolean;
    selectedTools: any[];
    titleSummarySettings?: any;
  }): Promise<Response> {
    const url = '/api/chat';
    const headers = {
      ...this.defaultHeaders,
      ...this.getAuthHeaders(),
    };

    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`聊天请求失败: ${response.status}`);
    }

    return response;
  }

  /**
   * 加载对话
   */
  static async loadConversation(conversationId: number): Promise<any> {
    return this.get(`/api/conversations/${conversationId}`);
  }

  /**
   * 获取对话列表
   */
  static async getConversations(): Promise<any[]> {
    return this.get('/api/conversations');
  }

  /**
   * 创建新对话
   */
  static async createConversation(data: {
    title: string;
    model: string;
  }): Promise<{ id: number }> {
    return this.post('/api/conversations', data);
  }

  /**
   * 删除对话
   */
  static async deleteConversation(conversationId: number): Promise<void> {
    return this.delete(`/api/conversations/${conversationId}`);
  }

  /**
   * 清空对话
   */
  static async clearConversation(conversationId: number): Promise<void> {
    return this.post(`/api/conversations/${conversationId}/clear`);
  }

  /**
   * 更新对话标题
   */
  static async updateConversationTitle(
    conversationId: number,
    title: string
  ): Promise<void> {
    return this.put(`/api/conversations/${conversationId}`, { title });
  }

  /**
   * 获取智能体列表
   */
  static async getAgents(): Promise<any[]> {
    return this.get('/api/agents');
  }

  /**
   * 获取模型列表
   */
  static async getModels(): Promise<any[]> {
    return this.get('/api/models');
  }

  /**
   * 用户登录
   */
  static async login(credentials: {
    username: string;
    password: string;
  }): Promise<{ token: string; user: any }> {
    return this.post('/api/auth/login', credentials);
  }

  /**
   * 用户注册
   */
  static async register(userData: {
    username: string;
    password: string;
    email?: string;
  }): Promise<{ token: string; user: any }> {
    return this.post('/api/auth/register', userData);
  }

  /**
   * 获取用户信息
   */
  static async getUserInfo(): Promise<any> {
    return this.get('/api/auth/me');
  }

  /**
   * 上传文件
   */
  static async uploadFile(file: File, endpoint: string = '/api/upload'): Promise<any> {
    const formData = new FormData();
    formData.append('file', file);

    const headers = this.getAuthHeaders();
    // 不设置Content-Type，让浏览器自动设置multipart/form-data

    try {
      const response = await fetch(endpoint, {
        method: 'POST',
        headers,
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`文件上传失败: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      ErrorHandler.logError(error, '文件上传失败', { fileName: file.name });
      throw error;
    }
  }

  /**
   * 带重试的请求
   */
  static async requestWithRetry<T>(
    endpoint: string,
    options: RequestInit = {},
    maxRetries: number = 3,
    retryDelay: number = 1000
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await this.request<T>(endpoint, options);
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        
        if (attempt === maxRetries || !ErrorHandler.isRetryableError(error)) {
          break;
        }

        console.warn(`请求失败，第${attempt}次重试 (${maxRetries}次中)`, error);
        await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
      }
    }

    throw lastError!;
  }

  /**
   * 批量请求
   */
  static async batchRequest<T>(
    requests: Array<{ endpoint: string; options?: RequestInit }>
  ): Promise<Array<T | Error>> {
    const promises = requests.map(({ endpoint, options }) =>
      this.request<T>(endpoint, options).catch(error => error)
    );

    return Promise.all(promises);
  }

  /**
   * 检查网络连接
   */
  static async checkConnection(): Promise<boolean> {
    try {
      await this.get('/api/health');
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 设置基础URL
   */
  static setBaseUrl(url: string): void {
    this.baseUrl = url;
  }

  /**
   * 设置默认头部
   */
  static setDefaultHeaders(headers: Record<string, string>): void {
    this.defaultHeaders = { ...this.defaultHeaders, ...headers };
  }
}

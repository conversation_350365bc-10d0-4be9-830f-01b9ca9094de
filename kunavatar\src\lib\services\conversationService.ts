import { DbUtils } from '../utils/dbUtils';
import { ValidationUtils } from '../utils/validationUtils';

/**
 * 对话服务层 - 封装对话相关的业务逻辑
 */
export class ConversationService {
  /**
   * 获取用户的所有对话
   */
  static async getUserConversations(userId: number): Promise<{
    success: boolean;
    conversations?: any[];
    error?: string;
  }> {
    // 验证用户ID
    const userValidation = ValidationUtils.validateUserId(userId);
    if (!userValidation.isValid) {
      return { success: false, error: userValidation.error };
    }

    // 获取对话列表
    return DbUtils.getUserConversationsWithStats(userValidation.id!);
  }

  /**
   * 获取单个对话详情
   */
  static async getConversationDetails(
    conversationId: number,
    userId: number
  ): Promise<{
    success: boolean;
    conversation?: any;
    messages?: any[];
    toolCalls?: any[];
    lastModel?: string;
    error?: string;
  }> {
    // 验证参数
    const conversationValidation = ValidationUtils.validateConversationId(conversationId);
    if (!conversationValidation.isValid) {
      return { success: false, error: conversationValidation.error };
    }

    const userValidation = ValidationUtils.validateUserId(userId);
    if (!userValidation.isValid) {
      return { success: false, error: userValidation.error };
    }

    // 验证访问权限
    const accessCheck = DbUtils.validateConversationAccess(
      conversationValidation.id!,
      userValidation.id!
    );
    if (!accessCheck.hasAccess) {
      return { success: false, error: accessCheck.error };
    }

    // 获取消息
    const messagesResult = DbUtils.getConversationMessages(
      conversationValidation.id!,
      userValidation.id!
    );
    if (!messagesResult.success) {
      return { success: false, error: messagesResult.error };
    }

    // 获取工具调用记录
    const toolCallsResult = DbUtils.getConversationToolCalls(
      conversationValidation.id!,
      userValidation.id!
    );
    if (!toolCallsResult.success) {
      return { success: false, error: toolCallsResult.error };
    }

    // 获取最后使用的模型（这里需要实现相应的数据库方法）
    const lastModel = ''; // 暂时为空

    return {
      success: true,
      conversation: accessCheck.conversation,
      messages: messagesResult.messages,
      toolCalls: toolCallsResult.toolCalls,
      lastModel
    };
  }

  /**
   * 创建新对话
   */
  static async createConversation(
    title: string,
    model: string,
    userId: number
  ): Promise<{
    success: boolean;
    conversation?: any;
    error?: string;
  }> {
    // 验证参数
    const titleValidation = ValidationUtils.validateTitle(title);
    if (!titleValidation.isValid) {
      return { success: false, error: titleValidation.error };
    }

    const modelValidation = ValidationUtils.validateModelName(model);
    if (!modelValidation.isValid) {
      return { success: false, error: modelValidation.error };
    }

    const userValidation = ValidationUtils.validateUserId(userId);
    if (!userValidation.isValid) {
      return { success: false, error: userValidation.error };
    }

    // 创建对话
    const result = DbUtils.createConversation(title, model, userValidation.id!);
    if (!result.success) {
      return { success: false, error: result.error };
    }

    // 获取创建的对话详情
    const conversationDetails = await ConversationService.getConversationDetails(
      result.conversationId!,
      userValidation.id!
    );

    if (!conversationDetails.success) {
      return { success: false, error: conversationDetails.error };
    }

    return {
      success: true,
      conversation: conversationDetails.conversation
    };
  }

  /**
   * 删除对话
   */
  static async deleteConversation(
    conversationId: number,
    userId: number
  ): Promise<{
    success: boolean;
    error?: string;
  }> {
    // 验证参数
    const conversationValidation = ValidationUtils.validateConversationId(conversationId);
    if (!conversationValidation.isValid) {
      return { success: false, error: conversationValidation.error };
    }

    const userValidation = ValidationUtils.validateUserId(userId);
    if (!userValidation.isValid) {
      return { success: false, error: userValidation.error };
    }

    // 删除对话
    return DbUtils.deleteConversation(conversationValidation.id!, userValidation.id!);
  }

  /**
   * 清空对话
   */
  static async clearConversation(
    conversationId: number,
    userId: number
  ): Promise<{
    success: boolean;
    error?: string;
  }> {
    // 验证参数
    const conversationValidation = ValidationUtils.validateConversationId(conversationId);
    if (!conversationValidation.isValid) {
      return { success: false, error: conversationValidation.error };
    }

    const userValidation = ValidationUtils.validateUserId(userId);
    if (!userValidation.isValid) {
      return { success: false, error: userValidation.error };
    }

    // 清空对话
    return DbUtils.clearConversation(conversationValidation.id!, userValidation.id!);
  }

  /**
   * 保存用户消息
   */
  static async saveUserMessage(
    conversationId: number,
    content: string,
    model: string,
    userId: number
  ): Promise<{
    success: boolean;
    messageId?: number;
    error?: string;
  }> {
    // 验证参数
    const conversationValidation = ValidationUtils.validateConversationId(conversationId);
    if (!conversationValidation.isValid) {
      return { success: false, error: conversationValidation.error };
    }

    const userValidation = ValidationUtils.validateUserId(userId);
    if (!userValidation.isValid) {
      return { success: false, error: userValidation.error };
    }

    const modelValidation = ValidationUtils.validateModelName(model);
    if (!modelValidation.isValid) {
      return { success: false, error: modelValidation.error };
    }

    if (!content || content.trim().length === 0) {
      return { success: false, error: '消息内容不能为空' };
    }

    // 保存消息
    return DbUtils.saveUserMessage(
      conversationValidation.id!,
      content,
      model,
      userValidation.id!
    );
  }

  /**
   * 保存助手消息
   */
  static async saveAssistantMessage(
    conversationId: number,
    content: string,
    model: string,
    userId: number,
    stats?: any
  ): Promise<{
    success: boolean;
    messageId?: number;
    error?: string;
  }> {
    // 验证参数
    const conversationValidation = ValidationUtils.validateConversationId(conversationId);
    if (!conversationValidation.isValid) {
      return { success: false, error: conversationValidation.error };
    }

    const userValidation = ValidationUtils.validateUserId(userId);
    if (!userValidation.isValid) {
      return { success: false, error: userValidation.error };
    }

    const modelValidation = ValidationUtils.validateModelName(model);
    if (!modelValidation.isValid) {
      return { success: false, error: modelValidation.error };
    }

    if (!content || content.trim().length === 0) {
      return { success: false, error: '消息内容不能为空' };
    }

    // 保存消息
    return DbUtils.saveAssistantMessage(
      conversationValidation.id!,
      content,
      model,
      userValidation.id!,
      stats
    );
  }

  /**
   * 更新对话标题
   */
  static async updateConversationTitle(
    conversationId: number,
    title: string,
    userId: number
  ): Promise<{
    success: boolean;
    error?: string;
  }> {
    // 验证参数
    const conversationValidation = ValidationUtils.validateConversationId(conversationId);
    if (!conversationValidation.isValid) {
      return { success: false, error: conversationValidation.error };
    }

    const titleValidation = ValidationUtils.validateTitle(title);
    if (!titleValidation.isValid) {
      return { success: false, error: titleValidation.error };
    }

    const userValidation = ValidationUtils.validateUserId(userId);
    if (!userValidation.isValid) {
      return { success: false, error: userValidation.error };
    }

    // 验证访问权限
    const accessCheck = DbUtils.validateConversationAccess(
      conversationValidation.id!,
      userValidation.id!
    );
    if (!accessCheck.hasAccess) {
      return { success: false, error: accessCheck.error };
    }

    // 更新标题（这里需要实现相应的数据库方法）
    try {
      // dbOperations.updateConversationTitle(conversationValidation.id!, title);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '更新标题失败'
      };
    }
  }
}

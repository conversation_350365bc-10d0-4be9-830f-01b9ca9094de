'use client';

import { useEffect, useCallback } from 'react';
import { CustomModel } from '@/lib/database/custom-models';

interface UseMessageLoaderProps {
  currentConversation: any;
  setSelectedModel: (model: string, conversationId?: number) => void;
  setMessages: (messages: any[]) => void;
  setToolCalls: (toolCalls: any[]) => void;
  selectedModel?: string; // 当前选择的模型
  models?: CustomModel[]; // 可用模型列表
  selectBestModel?: (
    availableModels: CustomModel[],
    conversationId?: number,
    lastUsedModel?: string,
    conversationModel?: string
  ) => string | undefined; // 智能模型选择函数
}

export function useMessageLoader({
  currentConversation,
  setSelectedModel,
  setMessages,
  setToolCalls,
  selectedModel,
  models,
  selectBestModel,
}: UseMessageLoaderProps) {

  // 加载对话消息历史 - 优化：添加缓存和重试机制
  const loadConversationMessages = useCallback(async (conversationId: number, retryCount = 0) => {
    try {
      console.log(`🔄 加载对话 ${conversationId} 的消息 (尝试 ${retryCount + 1})`);

      const token = localStorage.getItem('accessToken');
      const response = await fetch(`/api/conversations/${conversationId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });
      
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('对话不存在 (404)');
        }
        throw new Error(`加载对话失败: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.success && data.messages) {
        // 处理消息格式转换逻辑
        const allMessages = data.messages.sort((a: any, b: any) => {
          if (a.timestamp !== b.timestamp) {
            return a.timestamp - b.timestamp;
          }
          return a.id - b.id;
        });
        
        const formattedMessages: any[] = [];
        const toolCallMessages: any[] = [];
        
        for (const msg of allMessages) {
          if (msg.role === 'tool_call' && msg.tool_name) {
            // 处理工具调用消息
            let args = {};
            let result = '';
            
            try {
              args = msg.tool_args ? JSON.parse(msg.tool_args) : {};
            } catch (e) {
              args = {};
            }
            
            try {
              result = msg.tool_result ? 
                (typeof msg.tool_result === 'string' ? msg.tool_result : JSON.stringify(msg.tool_result)) 
                : '';
            } catch (e) {
              result = msg.tool_result || '';
            }
            
            const toolCall = {
              id: `tool-${msg.id}`,
              toolName: msg.tool_name,
              args: args,
              status: msg.tool_status || 'completed',
              result: result,
              error: msg.tool_error || undefined,
              startTime: msg.timestamp || new Date(msg.created_at).getTime(),
              executionTime: msg.tool_execution_time || 0,
            };
            
            toolCallMessages.push(toolCall);
            
            formattedMessages.push({
              id: `tool-placeholder-${msg.id}`,
              role: 'tool_call' as any,
              content: '',
              timestamp: msg.timestamp || new Date(msg.created_at).getTime(),
              toolCall: toolCall,
            });
          } else {
            formattedMessages.push({
              id: `msg-${msg.id}`,
              role: msg.role,
              content: msg.content,
              timestamp: msg.timestamp || new Date(msg.created_at).getTime(),
              model: msg.model,
              // 添加统计字段
              total_duration: msg.total_duration,
              load_duration: msg.load_duration,
              prompt_eval_count: msg.prompt_eval_count,
              prompt_eval_duration: msg.prompt_eval_duration,
              eval_count: msg.eval_count,
              eval_duration: msg.eval_duration,
            });
          }
        }
        
        console.log(`✅ 成功加载对话 ${conversationId} 的 ${formattedMessages.length} 条消息`);
        setMessages(formattedMessages);
        setToolCalls(toolCallMessages);
        
        // 返回对话中最后使用的模型
        return data.lastModel;
      } else {
        console.log(`⚠️ 对话 ${conversationId} 没有消息数据`);
        setMessages([]);
        setToolCalls([]);
        return null;
      }
    } catch (err) {
      console.error('加载对话消息失败:', err);
      throw new Error('加载对话消息失败');
    }
  }, [setMessages, setToolCalls]);

  // 当切换对话时，加载对话的消息历史
  useEffect(() => {
    if (currentConversation) {
      console.log(`📨 消息加载器 - 开始加载对话 ${currentConversation.id} 的消息`);
      
      // 清空当前消息和工具调用
      setMessages([]);
      setToolCalls([]);
      
      // 加载对话消息和最后使用的模型
      loadConversationMessages(currentConversation.id)
        .then(lastUsedModel => {
          // 使用智能模型选择功能
          if (selectBestModel && models) {
            const selectedModelName = selectBestModel(
              models,
              currentConversation.id,
              lastUsedModel,
              currentConversation.model
            );
            
            if (selectedModelName) {
              console.log(`🎯 为对话 ${currentConversation.id} 选择模型: ${selectedModelName}`);
            }
          }
          console.log(`✅ 消息加载器 - 完成对话 ${currentConversation.id} 的消息加载`);
        })
        .catch(error => {
          console.error('加载消息失败:', error);
        });
    } else {
      console.log(`🧹 消息加载器 - 清空消息和工具调用`);
      setMessages([]);
      setToolCalls([]);
    }
  }, [currentConversation?.id, loadConversationMessages, setMessages, setToolCalls, models, selectBestModel]);

  return {
    loadConversationMessages,
  };
}
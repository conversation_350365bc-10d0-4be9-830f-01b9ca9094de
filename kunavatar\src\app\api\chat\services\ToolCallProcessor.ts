import { ChatMessage, Tool, ollamaClient } from '../../../../lib/ollama';
import { ToolExecutionService, StreamController } from './toolExecutionService';
import { MessageStorageService } from './messageStorageService';
import { StreamingChatRequest } from './streamingChatHandler';
import { ToolExecutor } from '../../../../lib/tools';

/**
 * 工具调用处理器 - 专门处理工具调用相关逻辑
 */
export class ToolCallProcessor {
  /**
   * 在流中处理工具调用
   */
  static async handleToolCallsInStream(
    toolCalls: any[],
    chatRequest: StreamingChatRequest,
    assistantMessage: string,
    streamController: StreamController
  ): Promise<void> {
    const { controller, encoder } = streamController;
    
    try {
      // 先保存当前的助手消息（如果有内容）
      if (assistantMessage.trim() && chatRequest.conversationId) {
        await MessageStorageService.saveAssistantMessage(
          chatRequest.conversationId,
          assistantMessage,
          chatRequest.model,
          chatRequest.userId,
          undefined // 工具调用时不保存统计信息
        );
      }
      
      // 处理每个工具调用
      for (const toolCall of toolCalls) {
        await ToolCallProcessor.processToolCall(
          toolCall,
          chatRequest,
          streamController
        );
      }
      
      // 工具调用完成后，继续对话
      await ToolCallProcessor.continueAfterToolCalls(
        toolCalls,
        chatRequest,
        streamController
      );
      
    } catch (error) {
      console.error('处理工具调用时出错:', error);
      
      // 发送工具调用错误
      const errorData = {
        type: 'tool_error',
        error: error instanceof Error ? error.message : '工具调用失败'
      };
      controller.enqueue(encoder.encode(`data: ${JSON.stringify(errorData)}\n\n`));
    }
  }

  /**
   * 处理单个工具调用
   */
  static async processToolCall(
    toolCall: any,
    chatRequest: StreamingChatRequest,
    streamController: StreamController
  ): Promise<void> {
    const { controller, encoder } = streamController;
    
    try {
      console.log('🔧 开始执行工具调用:', toolCall.function?.name);

      // 安全解析工具参数
      let args: any = {};
      try {
        const argumentsValue = toolCall.function?.arguments;
        if (typeof argumentsValue === 'string') {
          args = JSON.parse(argumentsValue || '{}');
        } else if (typeof argumentsValue === 'object' && argumentsValue !== null) {
          args = argumentsValue;
        } else {
          args = {};
        }
      } catch (error) {
        console.error('解析工具参数失败:', error, '原始参数:', toolCall.function?.arguments);
        args = {};
      }

      // 发送工具调用开始事件
      const toolStartData = {
        type: 'tool_call_start',
        tool_call_id: toolCall.id || `tool-${Date.now()}`,
        tool_name: toolCall.function?.name,
        tool_args: args
      };
      controller.enqueue(encoder.encode(`data: ${JSON.stringify(toolStartData)}\n\n`));

      // 执行工具
      const startTime = Date.now();
      const selectedTool = chatRequest.userSelectedTools.find(tool => tool.function.name === toolCall.function?.name);
      const serverName = (selectedTool as any)?.serverName;

      const toolResult = await ToolExecutor.executeToolCall(toolCall.function?.name || '', args, serverName);
      const executionTime = Date.now() - startTime;

      const result = {
        result: toolResult,
        status: 'completed' as const,
        executionTime
      };
      
      // 保存工具调用结果到数据库
      if (chatRequest.conversationId) {
        await MessageStorageService.saveToolMessage(
          chatRequest.conversationId,
          result.result || '',
          'ollama', // 默认模型
          chatRequest.userId
        );
      }
      
      // 发送工具调用完成事件
      const toolCompleteData = {
        type: 'tool_call_complete',
        tool_call_id: toolCall.id || `tool-${Date.now()}`,
        tool_name: toolCall.function?.name,
        tool_result: result.result,
        execution_time: result.executionTime
      };
      controller.enqueue(encoder.encode(`data: ${JSON.stringify(toolCompleteData)}\n\n`));
      
    } catch (error) {
      console.error('执行工具调用时出错:', error);
      
      // 保存错误的工具调用
      if (chatRequest.conversationId) {
        await MessageStorageService.saveToolMessage(
          chatRequest.conversationId,
          `工具执行失败: ${error instanceof Error ? error.message : '工具执行失败'}`,
          'ollama', // 默认模型
          chatRequest.userId
        );
      }
      
      // 发送工具调用错误事件
      const toolErrorData = {
        type: 'tool_call_error',
        tool_call_id: toolCall.id || `tool-${Date.now()}`,
        tool_name: toolCall.function?.name,
        error_message: error instanceof Error ? error.message : '工具执行失败'
      };
      controller.enqueue(encoder.encode(`data: ${JSON.stringify(toolErrorData)}\n\n`));
    }
  }

  /**
   * 工具调用完成后继续对话
   */
  static async continueAfterToolCalls(
    toolCalls: any[],
    chatRequest: StreamingChatRequest,
    streamController: StreamController
  ): Promise<void> {
    const { controller, encoder } = streamController;
    
    try {
      // 构建包含工具调用结果的消息历史
      const updatedMessages = await ToolCallProcessor.buildMessagesWithToolResults(
        chatRequest,
        toolCalls
      );
      
      // 发送继续对话的信号
      const continueData = {
        type: 'continue_after_tools',
        message: '工具调用完成，继续生成回复...'
      };
      controller.enqueue(encoder.encode(`data: ${JSON.stringify(continueData)}\n\n`));
      
      // 创建新的聊天请求（不包含工具，避免循环调用）
      const continueRequest = {
        model: chatRequest.model,
        messages: updatedMessages,
        stream: true,
        options: chatRequest.options || {}
      };
      
      // 继续流式对话
      let assistantMessage = '';
      for await (const chunk of ollamaClient.chatStream(continueRequest)) {
        if (chunk.message?.content) {
          assistantMessage += chunk.message.content;
          
          // 发送内容更新
          const contentData = {
            message: chunk.message.content
          };
          controller.enqueue(encoder.encode(`data: ${JSON.stringify(contentData)}\n\n`));
        }
      }
      
      // 保存工具调用后的助手回复
      if (assistantMessage.trim() && chatRequest.conversationId) {
        await MessageStorageService.saveAssistantMessage(
          chatRequest.conversationId,
          assistantMessage,
          chatRequest.model,
          chatRequest.userId,
          undefined
        );
      }
      
    } catch (error) {
      console.error('工具调用后继续对话时出错:', error);
      
      const errorData = {
        type: 'error',
        error: '工具调用后继续对话失败'
      };
      controller.enqueue(encoder.encode(`data: ${JSON.stringify(errorData)}\n\n`));
    }
  }

  /**
   * 构建包含工具调用结果的消息历史
   */
  static async buildMessagesWithToolResults(
    chatRequest: StreamingChatRequest,
    toolCalls: any[]
  ): Promise<ChatMessage[]> {
    const messages = [...chatRequest.messages];
    
    // 添加助手的工具调用消息
    const assistantMessage: ChatMessage = {
      role: 'assistant',
      content: '',
      tool_calls: toolCalls
    };
    messages.push(assistantMessage);
    
    // 为每个工具调用添加结果消息
    for (const toolCall of toolCalls) {
      try {
        // 执行工具调用获取结果
        const args = typeof toolCall.function?.arguments === 'string'
          ? JSON.parse(toolCall.function.arguments || '{}')
          : toolCall.function?.arguments || {};

        const selectedTool = chatRequest.userSelectedTools.find(tool => tool.function.name === toolCall.function?.name);
        const serverName = (selectedTool as any)?.serverName;

        const toolResult = await ToolExecutor.executeToolCall(
          toolCall.function?.name || '',
          args,
          serverName
        );

        const toolMessage: ChatMessage = {
          role: 'tool',
          content: typeof toolResult === 'string' ? toolResult : JSON.stringify(toolResult)
        };
        messages.push(toolMessage);

      } catch (error) {
        console.error('执行工具调用时出错:', error);

        const errorMessage: ChatMessage = {
          role: 'tool',
          content: `工具调用失败: ${error instanceof Error ? error.message : '未知错误'}`
        };
        messages.push(errorMessage);
      }
    }
    
    return messages;
  }

}

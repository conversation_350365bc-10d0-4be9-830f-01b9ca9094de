import { dbOperations, mcpDbOperations } from '../database';

/**
 * 数据库操作工具类
 */
export class DbUtils {
  /**
   * 验证用户对对话的访问权限
   */
  static validateConversationAccess(
    conversationId: number, 
    userId: number
  ): { hasAccess: boolean; conversation?: any; error?: string } {
    try {
      const conversation = dbOperations.getConversationByIdAndUserId(conversationId, String(userId));
      
      if (!conversation) {
        return { 
          hasAccess: false, 
          error: '对话不存在或无权限访问' 
        };
      }

      return { hasAccess: true, conversation };
    } catch (error) {
      return { 
        hasAccess: false, 
        error: error instanceof Error ? error.message : '数据库查询失败' 
      };
    }
  }

  /**
   * 安全地获取对话消息
   */
  static getConversationMessages(
    conversationId: number, 
    userId: number
  ): { success: boolean; messages?: any[]; error?: string } {
    try {
      // 先验证访问权限
      const accessCheck = DbUtils.validateConversationAccess(conversationId, userId);
      if (!accessCheck.hasAccess) {
        return { success: false, error: accessCheck.error };
      }

      const messages = dbOperations.getMessagesByConversationIdAndUserId(conversationId, String(userId));
      return { success: true, messages };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '获取消息失败' 
      };
    }
  }

  /**
   * 安全地获取工具调用记录
   */
  static getConversationToolCalls(
    conversationId: number, 
    userId: number
  ): { success: boolean; toolCalls?: any[]; error?: string } {
    try {
      // 先验证访问权限
      const accessCheck = DbUtils.validateConversationAccess(conversationId, userId);
      if (!accessCheck.hasAccess) {
        return { success: false, error: accessCheck.error };
      }

      const toolCalls = dbOperations.getToolCallsByConversationIdAndUserId(conversationId, String(userId));
      return { success: true, toolCalls };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '获取工具调用记录失败' 
      };
    }
  }

  /**
   * 安全地创建对话
   */
  static createConversation(
    title: string, 
    model: string, 
    userId: number
  ): { success: boolean; conversationId?: number; error?: string } {
    try {
      const conversationId = dbOperations.createConversation({
        title: title.trim(),
        model,
        user_id: String(userId)
      });

      return { success: true, conversationId };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '创建对话失败' 
      };
    }
  }

  /**
   * 安全地删除对话
   */
  static deleteConversation(
    conversationId: number, 
    userId: number
  ): { success: boolean; error?: string } {
    try {
      // 先验证访问权限
      const accessCheck = DbUtils.validateConversationAccess(conversationId, userId);
      if (!accessCheck.hasAccess) {
        return { success: false, error: accessCheck.error };
      }

      dbOperations.deleteConversation(conversationId);
      return { success: true };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '删除对话失败' 
      };
    }
  }

  /**
   * 安全地清空对话
   */
  static clearConversation(
    conversationId: number, 
    userId: number
  ): { success: boolean; error?: string } {
    try {
      // 先验证访问权限
      const accessCheck = DbUtils.validateConversationAccess(conversationId, userId);
      if (!accessCheck.hasAccess) {
        return { success: false, error: accessCheck.error };
      }

      dbOperations.deleteMessagesByConversationId(conversationId);
      return { success: true };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '清空对话失败' 
      };
    }
  }

  /**
   * 获取用户的对话列表（带统计信息）
   */
  static getUserConversationsWithStats(
    userId: number
  ): { success: boolean; conversations?: any[]; error?: string } {
    try {
      const conversations = dbOperations.getAllConversationsByUserId(String(userId));

      // 为每个对话添加统计信息
      const conversationsWithStats = conversations.map(conversation => {
        try {
          const stats = dbOperations.getConversationStats(conversation.id);
          return { ...conversation, stats };
        } catch (error) {
          console.warn(`获取对话${conversation.id}统计信息失败:`, error);
          return { ...conversation, stats: null };
        }
      });

      return { success: true, conversations: conversationsWithStats };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '获取对话列表失败' 
      };
    }
  }

  /**
   * 安全地保存用户消息
   */
  static saveUserMessage(
    conversationId: number,
    content: string,
    model: string,
    userId: number
  ): { success: boolean; messageId?: number; error?: string } {
    try {
      // 先验证访问权限
      const accessCheck = DbUtils.validateConversationAccess(conversationId, userId);
      if (!accessCheck.hasAccess) {
        return { success: false, error: accessCheck.error };
      }

      const messageId = dbOperations.createMessage({
        conversation_id: conversationId,
        role: 'user',
        content: content.trim(),
        model,
        user_id: String(userId)
      });

      return { success: true, messageId };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '保存用户消息失败' 
      };
    }
  }

  /**
   * 安全地保存助手消息
   */
  static saveAssistantMessage(
    conversationId: number,
    content: string,
    model: string,
    userId: number,
    stats?: any
  ): { success: boolean; messageId?: number; error?: string } {
    try {
      // 先验证访问权限
      const accessCheck = DbUtils.validateConversationAccess(conversationId, userId);
      if (!accessCheck.hasAccess) {
        return { success: false, error: accessCheck.error };
      }

      const messageData: any = {
        conversation_id: conversationId,
        role: 'assistant',
        content: content.trim(),
        model,
        user_id: userId,
        timestamp: Date.now()
      };

      // 添加统计信息
      if (stats) {
        Object.assign(messageData, {
          total_duration: stats.total_duration,
          load_duration: stats.load_duration,
          prompt_eval_count: stats.prompt_eval_count,
          prompt_eval_duration: stats.prompt_eval_duration,
          eval_count: stats.eval_count,
          eval_duration: stats.eval_duration,
        });
      }

      const messageId = dbOperations.createMessage(messageData);
      return { success: true, messageId };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '保存助手消息失败' 
      };
    }
  }

  /**
   * 批量操作的事务包装器
   */
  static async withTransaction<T>(
    operation: () => T
  ): Promise<{ success: boolean; result?: T; error?: string }> {
    try {
      // 注意：这里假设数据库支持事务，实际实现可能需要调整
      const result = operation();
      return { success: true, result };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '事务执行失败' 
      };
    }
  }

  /**
   * 检查资源是否存在
   */
  static checkResourceExists(
    resourceType: 'conversation' | 'user' | 'agent',
    id: number
  ): boolean {
    try {
      switch (resourceType) {
        case 'conversation':
          return !!dbOperations.getConversationById(id);
        case 'user':
          return !!mcpDbOperations.getUserById(String(id));
        case 'agent':
          // 假设有相应的方法
          return true; // 暂时返回true
        default:
          return false;
      }
    } catch (error) {
      console.error(`检查${resourceType}资源存在性失败:`, error);
      return false;
    }
  }

  /**
   * 获取资源的最后更新时间
   */
  static getLastUpdateTime(
    resourceType: 'conversation',
    id: number
  ): { success: boolean; timestamp?: number; error?: string } {
    try {
      switch (resourceType) {
        case 'conversation':
          const conversation = dbOperations.getConversationById(id);
          if (!conversation) {
            return { success: false, error: '对话不存在' };
          }
          return { 
            success: true, 
            timestamp: new Date(conversation.updated_at).getTime() 
          };
        default:
          return { success: false, error: '不支持的资源类型' };
      }
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '获取更新时间失败' 
      };
    }
  }
}

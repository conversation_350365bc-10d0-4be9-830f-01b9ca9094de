import { SimpleMessage } from '../types';
import { Conversation } from '@/lib/database';
import { CustomModel } from '@/lib/database/custom-models';
import { AgentWithRelations } from '@/app/agents/types';

/**
 * 聊天容器组件的Props
 */
export interface ChatContainerProps {
  // 基础状态
  currentConversation: Conversation | null;
  messages: SimpleMessage[];
  isStreaming: boolean;
  error: string | null;
  
  // 模型和智能体
  models: CustomModel[];
  selectedModel: string;
  agents: AgentWithRelations[];
  selectedAgentId: number | null;
  
  // 输入和交互
  inputMessage: string;
  onInputChange: (message: string) => void;
  onSendMessage: () => void;
  onStopGeneration: () => void;
  
  // 工具相关
  enableTools: boolean;
  selectedTools: any[];
  onToolsToggle: (enabled: boolean) => void;
  onSelectedToolsChange: (tools: any[]) => void;
  
  // UI配置
  chatStyle: 'bubble' | 'conversation';
  displaySize: 'compact' | 'normal' | 'large';
  isMemoryVisible: boolean;
  expandedThinkingMessages: Set<string>;
  
  // 事件处理
  onModelChange: (model: string) => void;
  onAgentChange: (agentId: number | null) => void;
  onToggleThinkingExpand: (messageId: string) => void;
  onInsertText: (text: string) => void;
  onClearChat: () => void;
  onDismissError: () => void;
  onChatStyleChange: (style: 'bubble' | 'conversation') => void;
  onDisplaySizeChange: (size: 'compact' | 'normal' | 'large') => void;
  onMemoryToggle: () => void;
  
  // 选择器模式
  selectorMode: 'model' | 'agent';
  onSelectorModeChange: (mode: 'model' | 'agent') => void;
  
  // 自定义模型显示
  customModels: Array<{
    base_model: string;
    display_name: string;
    family?: string;
  }>;
}

/**
 * 消息列表组件的Props
 */
export interface MessageListProps {
  messages: SimpleMessage[];
  isStreaming: boolean;
  expandedThinkingMessages: Set<string>;
  onToggleThinkingExpand: (messageId: string) => void;
  chatStyle: 'bubble' | 'conversation';
  selectedModel?: string;
  customModels?: Array<{
    base_model: string;
    display_name: string;
    family?: string;
  }>;
  selectedAgent?: AgentWithRelations | null;
}

/**
 * 消息输入组件的Props
 */
export interface MessageInputProps {
  inputMessage: string;
  onInputChange: (message: string) => void;
  onSendMessage: () => void;
  onStopGeneration: () => void;
  isStreaming: boolean;
  enableTools: boolean;
  selectedTools: any[];
  onToolsToggle: (enabled: boolean) => void;
  onSelectedToolsChange: (tools: any[]) => void;
  onInsertText: (text: string) => void;
  disabled?: boolean;
}

/**
 * 聊天头部组件的Props
 */
export interface ChatHeaderProps {
  currentConversation: Conversation | null;
  models: CustomModel[];
  selectedModel: string;
  onModelChange: (model: string) => void;
  agents: AgentWithRelations[];
  selectedAgentId: number | null;
  onAgentChange: (agentId: number | null) => void;
  selectorMode: 'model' | 'agent';
  onSelectorModeChange: (mode: 'model' | 'agent') => void;
  customModels: Array<{
    base_model: string;
    display_name: string;
    family?: string;
  }>;
  onClearChat: () => void;
  chatStyle: 'bubble' | 'conversation';
  displaySize: 'compact' | 'normal' | 'large';
  onChatStyleChange: (style: 'bubble' | 'conversation') => void;
  onDisplaySizeChange: (size: 'compact' | 'normal' | 'large') => void;
  isMemoryVisible: boolean;
  onMemoryToggle: () => void;
}

/**
 * 侧边栏组件的Props
 */
export interface SidebarProps {
  conversations: Conversation[];
  currentConversation: Conversation | null;
  onCreateConversation: () => void;
  onLoadConversation: (conversationId: number) => void;
  onDeleteConversation: (conversationId: number) => void;
  loading?: boolean;
}

/**
 * 工具面板组件的Props
 */
export interface ToolPanelProps {
  enableTools: boolean;
  selectedTools: any[];
  onToolsToggle: (enabled: boolean) => void;
  onSelectedToolsChange: (tools: any[]) => void;
  activeTool: any | null;
  toolCalls: any[];
}

/**
 * 错误显示组件的Props
 */
export interface ErrorDisplayProps {
  error: string | null;
  onDismiss: () => void;
  type?: 'error' | 'warning' | 'info';
}

/**
 * 加载状态组件的Props
 */
export interface LoadingStateProps {
  isLoading: boolean;
  message?: string;
  fullScreen?: boolean;
}

/**
 * 统一的事件处理器接口
 */
export interface ChatEventHandlers {
  onSendMessage: () => void;
  onStopGeneration: () => void;
  onInputChange: (message: string) => void;
  onModelChange: (model: string) => void;
  onAgentChange: (agentId: number | null) => void;
  onToolsToggle: (enabled: boolean) => void;
  onSelectedToolsChange: (tools: any[]) => void;
  onToggleThinkingExpand: (messageId: string) => void;
  onInsertText: (text: string) => void;
  onClearChat: () => void;
  onDismissError: () => void;
  onChatStyleChange: (style: 'bubble' | 'conversation') => void;
  onDisplaySizeChange: (size: 'compact' | 'normal' | 'large') => void;
  onMemoryToggle: () => void;
  onSelectorModeChange: (mode: 'model' | 'agent') => void;
  onCreateConversation: () => void;
  onLoadConversation: (conversationId: number) => void;
  onDeleteConversation: (conversationId: number) => void;
}

/**
 * 聊天配置接口
 */
export interface ChatConfig {
  chatStyle: 'bubble' | 'conversation';
  displaySize: 'compact' | 'normal' | 'large';
  enableTools: boolean;
  selectedTools: any[];
  isMemoryVisible: boolean;
  selectorMode: 'model' | 'agent';
}

/**
 * 聊天状态接口
 */
export interface ChatStatus {
  isStreaming: boolean;
  isInitializing: boolean;
  conversationLoading: boolean;
  error: string | null;
  conversationError: string | null;
}

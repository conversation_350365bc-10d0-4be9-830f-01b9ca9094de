import { z } from 'zod';

/**
 * 数据验证工具类
 */
export class ValidationUtils {
  /**
   * 验证聊天消息格式
   */
  static validateChatMessage(message: any): { isValid: boolean; error?: string } {
    if (!message || typeof message !== 'object') {
      return { isValid: false, error: '消息格式无效' };
    }

    if (!message.role || !['user', 'assistant', 'system', 'tool'].includes(message.role)) {
      return { isValid: false, error: '消息角色无效' };
    }

    if (typeof message.content !== 'string') {
      return { isValid: false, error: '消息内容必须是字符串' };
    }

    return { isValid: true };
  }

  /**
   * 验证消息数组
   */
  static validateMessages(messages: any[]): { isValid: boolean; error?: string } {
    if (!Array.isArray(messages) || messages.length === 0) {
      return { isValid: false, error: '消息列表不能为空' };
    }

    for (let i = 0; i < messages.length; i++) {
      const validation = ValidationUtils.validateChatMessage(messages[i]);
      if (!validation.isValid) {
        return { isValid: false, error: `第${i + 1}条消息${validation.error}` };
      }
    }

    return { isValid: true };
  }

  /**
   * 验证模型名称
   */
  static validateModelName(model: string): { isValid: boolean; error?: string } {
    if (!model || typeof model !== 'string' || model.trim().length === 0) {
      return { isValid: false, error: '模型名称不能为空' };
    }

    // 检查模型名称格式
    const modelNameRegex = /^[a-zA-Z0-9_\-.:]+$/;
    if (!modelNameRegex.test(model)) {
      return { isValid: false, error: '模型名称格式无效' };
    }

    return { isValid: true };
  }

  /**
   * 验证对话ID
   */
  static validateConversationId(conversationId: any): { 
    isValid: boolean; 
    id?: number; 
    error?: string 
  } {
    if (conversationId === undefined || conversationId === null) {
      return { isValid: true }; // 允许为空，表示新对话
    }

    const id = typeof conversationId === 'string' ? parseInt(conversationId) : conversationId;
    
    if (isNaN(id) || id <= 0) {
      return { isValid: false, error: '对话ID必须是正整数' };
    }

    return { isValid: true, id };
  }

  /**
   * 验证用户ID
   */
  static validateUserId(userId: any): { isValid: boolean; id?: number; error?: string } {
    if (!userId) {
      return { isValid: false, error: '用户ID不能为空' };
    }

    const id = typeof userId === 'string' ? parseInt(userId) : userId;
    
    if (isNaN(id) || id <= 0) {
      return { isValid: false, error: '用户ID必须是正整数' };
    }

    return { isValid: true, id };
  }

  /**
   * 验证智能体ID
   */
  static validateAgentId(agentId: any): { 
    isValid: boolean; 
    id?: number | null; 
    error?: string 
  } {
    if (agentId === undefined || agentId === null) {
      return { isValid: true, id: null }; // 允许为空
    }

    const id = typeof agentId === 'string' ? parseInt(agentId) : agentId;
    
    if (isNaN(id) || id <= 0) {
      return { isValid: false, error: '智能体ID必须是正整数' };
    }

    return { isValid: true, id };
  }

  /**
   * 验证工具配置
   */
  static validateToolConfiguration(
    enableTools: boolean, 
    selectedTools: any[]
  ): { isValid: boolean; error?: string } {
    if (typeof enableTools !== 'boolean') {
      return { isValid: false, error: 'enableTools必须是布尔值' };
    }

    if (!Array.isArray(selectedTools)) {
      return { isValid: false, error: 'selectedTools必须是数组' };
    }

    if (enableTools && selectedTools.length === 0) {
      return { isValid: false, error: '启用工具时必须选择至少一个工具' };
    }

    return { isValid: true };
  }

  /**
   * 验证标题
   */
  static validateTitle(title: string): { isValid: boolean; error?: string } {
    if (!title || typeof title !== 'string') {
      return { isValid: false, error: '标题不能为空' };
    }

    const trimmedTitle = title.trim();
    if (trimmedTitle.length === 0) {
      return { isValid: false, error: '标题不能为空' };
    }

    if (trimmedTitle.length > 200) {
      return { isValid: false, error: '标题长度不能超过200个字符' };
    }

    return { isValid: true };
  }

  /**
   * 验证邮箱格式
   */
  static validateEmail(email: string): { isValid: boolean; error?: string } {
    if (!email || typeof email !== 'string') {
      return { isValid: false, error: '邮箱不能为空' };
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return { isValid: false, error: '邮箱格式无效' };
    }

    return { isValid: true };
  }

  /**
   * 验证用户名格式
   */
  static validateUsername(username: string): { isValid: boolean; error?: string } {
    if (!username || typeof username !== 'string') {
      return { isValid: false, error: '用户名不能为空' };
    }

    if (username.length < 3 || username.length > 50) {
      return { isValid: false, error: '用户名长度必须在3-50个字符之间' };
    }

    const usernameRegex = /^[a-zA-Z0-9_]+$/;
    if (!usernameRegex.test(username)) {
      return { isValid: false, error: '用户名只能包含字母、数字和下划线' };
    }

    return { isValid: true };
  }

  /**
   * 验证密码强度
   */
  static validatePassword(password: string): { isValid: boolean; error?: string } {
    if (!password || typeof password !== 'string') {
      return { isValid: false, error: '密码不能为空' };
    }

    if (password.length < 6 || password.length > 128) {
      return { isValid: false, error: '密码长度必须在6-128个字符之间' };
    }

    return { isValid: true };
  }

  /**
   * 验证文件大小
   */
  static validateFileSize(size: number, maxSize: number = 10 * 1024 * 1024): { 
    isValid: boolean; 
    error?: string 
  } {
    if (size > maxSize) {
      return { 
        isValid: false, 
        error: `文件大小不能超过${Math.round(maxSize / 1024 / 1024)}MB` 
      };
    }

    return { isValid: true };
  }

  /**
   * 验证文件类型
   */
  static validateFileType(
    filename: string, 
    allowedTypes: string[]
  ): { isValid: boolean; error?: string } {
    const extension = filename.split('.').pop()?.toLowerCase();
    
    if (!extension || !allowedTypes.includes(extension)) {
      return { 
        isValid: false, 
        error: `不支持的文件类型，仅支持：${allowedTypes.join(', ')}` 
      };
    }

    return { isValid: true };
  }

  /**
   * 清理和验证文本内容
   */
  static sanitizeText(text: string, maxLength?: number): string {
    if (!text || typeof text !== 'string') {
      return '';
    }

    // 移除潜在的恶意脚本
    let sanitized = text
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '');

    // 限制长度
    if (maxLength && sanitized.length > maxLength) {
      sanitized = sanitized.substring(0, maxLength);
    }

    return sanitized.trim();
  }

  /**
   * 批量验证对象属性
   */
  static validateObject(
    obj: any, 
    schema: Record<string, (value: any) => { isValid: boolean; error?: string }>
  ): { isValid: boolean; errors: Record<string, string> } {
    const errors: Record<string, string> = {};
    let isValid = true;

    for (const [key, validator] of Object.entries(schema)) {
      const result = validator(obj[key]);
      if (!result.isValid) {
        errors[key] = result.error || '验证失败';
        isValid = false;
      }
    }

    return { isValid, errors };
  }
}

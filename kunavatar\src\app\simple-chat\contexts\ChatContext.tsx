'use client';

import React, { createContext, useContext, useReducer, ReactNode } from 'react';
import { SimpleMessage } from '../types';
import { Conversation } from '@/lib/database';
import { CustomModel } from '@/lib/database/custom-models';
import { AgentWithRelations } from '@/app/agents/types';

// 状态类型定义
interface ChatState {
  // 对话相关
  conversations: Conversation[];
  currentConversation: Conversation | null;
  conversationLoading: boolean;
  conversationError: string | null;
  
  // 消息相关
  messages: SimpleMessage[];
  inputMessage: string;
  isStreaming: boolean;
  expandedThinkingMessages: Set<string>;
  
  // 模型和智能体
  selectedModel: string;
  models: CustomModel[];
  agents: AgentWithRelations[];
  selectedAgentId: number | null;
  
  // 工具相关
  enableTools: boolean;
  selectedTools: any[];
  toolCalls: any[];
  activeTool: any | null;
  
  // UI状态
  error: string | null;
  isInitializing: boolean;
  chatStyle: 'bubble' | 'conversation';
  displaySize: 'compact' | 'normal' | 'large';
  isMemoryVisible: boolean;
  
  // 其他
  systemPrompt: string;
  currentAssistantMessageId: string | null;
  abortController: AbortController | null;
}

// 动作类型定义
type ChatAction =
  | { type: 'SET_CONVERSATIONS'; payload: Conversation[] }
  | { type: 'SET_CURRENT_CONVERSATION'; payload: Conversation | null }
  | { type: 'SET_CONVERSATION_LOADING'; payload: boolean }
  | { type: 'SET_CONVERSATION_ERROR'; payload: string | null }
  | { type: 'SET_MESSAGES'; payload: SimpleMessage[] }
  | { type: 'ADD_MESSAGE'; payload: SimpleMessage }
  | { type: 'UPDATE_MESSAGE'; payload: { id: string; updates: Partial<SimpleMessage> } }
  | { type: 'SET_INPUT_MESSAGE'; payload: string }
  | { type: 'SET_IS_STREAMING'; payload: boolean }
  | { type: 'TOGGLE_THINKING_EXPAND'; payload: string }
  | { type: 'SET_SELECTED_MODEL'; payload: string }
  | { type: 'SET_MODELS'; payload: CustomModel[] }
  | { type: 'SET_AGENTS'; payload: AgentWithRelations[] }
  | { type: 'SET_SELECTED_AGENT_ID'; payload: number | null }
  | { type: 'SET_ENABLE_TOOLS'; payload: boolean }
  | { type: 'SET_SELECTED_TOOLS'; payload: any[] }
  | { type: 'SET_TOOL_CALLS'; payload: any[] }
  | { type: 'ADD_TOOL_CALL'; payload: any }
  | { type: 'UPDATE_TOOL_CALL'; payload: { id: string; updates: any } }
  | { type: 'SET_ACTIVE_TOOL'; payload: any | null }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_IS_INITIALIZING'; payload: boolean }
  | { type: 'SET_CHAT_STYLE'; payload: 'bubble' | 'conversation' }
  | { type: 'SET_DISPLAY_SIZE'; payload: 'compact' | 'normal' | 'large' }
  | { type: 'SET_IS_MEMORY_VISIBLE'; payload: boolean }
  | { type: 'SET_SYSTEM_PROMPT'; payload: string }
  | { type: 'SET_CURRENT_ASSISTANT_MESSAGE_ID'; payload: string | null }
  | { type: 'SET_ABORT_CONTROLLER'; payload: AbortController | null }
  | { type: 'RESET_CHAT' };

// 初始状态
const initialState: ChatState = {
  conversations: [],
  currentConversation: null,
  conversationLoading: false,
  conversationError: null,
  messages: [],
  inputMessage: '',
  isStreaming: false,
  expandedThinkingMessages: new Set(),
  selectedModel: '',
  models: [],
  agents: [],
  selectedAgentId: null,
  enableTools: false,
  selectedTools: [],
  toolCalls: [],
  activeTool: null,
  error: null,
  isInitializing: true,
  chatStyle: 'conversation',
  displaySize: 'normal',
  isMemoryVisible: false,
  systemPrompt: '',
  currentAssistantMessageId: null,
  abortController: null,
};

// Reducer函数
function chatReducer(state: ChatState, action: ChatAction): ChatState {
  switch (action.type) {
    case 'SET_CONVERSATIONS':
      return { ...state, conversations: action.payload };
    
    case 'SET_CURRENT_CONVERSATION':
      return { ...state, currentConversation: action.payload };
    
    case 'SET_CONVERSATION_LOADING':
      return { ...state, conversationLoading: action.payload };
    
    case 'SET_CONVERSATION_ERROR':
      return { ...state, conversationError: action.payload };
    
    case 'SET_MESSAGES':
      return { ...state, messages: action.payload };
    
    case 'ADD_MESSAGE':
      return { ...state, messages: [...state.messages, action.payload] };
    
    case 'UPDATE_MESSAGE':
      return {
        ...state,
        messages: state.messages.map(msg =>
          msg.id === action.payload.id
            ? { ...msg, ...action.payload.updates }
            : msg
        )
      };
    
    case 'SET_INPUT_MESSAGE':
      return { ...state, inputMessage: action.payload };
    
    case 'SET_IS_STREAMING':
      return { ...state, isStreaming: action.payload };
    
    case 'TOGGLE_THINKING_EXPAND':
      const newExpandedSet = new Set(state.expandedThinkingMessages);
      if (newExpandedSet.has(action.payload)) {
        newExpandedSet.delete(action.payload);
      } else {
        newExpandedSet.add(action.payload);
      }
      return { ...state, expandedThinkingMessages: newExpandedSet };
    
    case 'SET_SELECTED_MODEL':
      return { ...state, selectedModel: action.payload };
    
    case 'SET_MODELS':
      return { ...state, models: action.payload };
    
    case 'SET_AGENTS':
      return { ...state, agents: action.payload };
    
    case 'SET_SELECTED_AGENT_ID':
      return { ...state, selectedAgentId: action.payload };
    
    case 'SET_ENABLE_TOOLS':
      return { ...state, enableTools: action.payload };
    
    case 'SET_SELECTED_TOOLS':
      return { ...state, selectedTools: action.payload };
    
    case 'SET_TOOL_CALLS':
      return { ...state, toolCalls: action.payload };
    
    case 'ADD_TOOL_CALL':
      return { ...state, toolCalls: [...state.toolCalls, action.payload] };
    
    case 'UPDATE_TOOL_CALL':
      return {
        ...state,
        toolCalls: state.toolCalls.map(tc =>
          tc.id === action.payload.id
            ? { ...tc, ...action.payload.updates }
            : tc
        )
      };
    
    case 'SET_ACTIVE_TOOL':
      return { ...state, activeTool: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    
    case 'SET_IS_INITIALIZING':
      return { ...state, isInitializing: action.payload };
    
    case 'SET_CHAT_STYLE':
      return { ...state, chatStyle: action.payload };
    
    case 'SET_DISPLAY_SIZE':
      return { ...state, displaySize: action.payload };
    
    case 'SET_IS_MEMORY_VISIBLE':
      return { ...state, isMemoryVisible: action.payload };
    
    case 'SET_SYSTEM_PROMPT':
      return { ...state, systemPrompt: action.payload };
    
    case 'SET_CURRENT_ASSISTANT_MESSAGE_ID':
      return { ...state, currentAssistantMessageId: action.payload };
    
    case 'SET_ABORT_CONTROLLER':
      return { ...state, abortController: action.payload };
    
    case 'RESET_CHAT':
      return {
        ...initialState,
        conversations: state.conversations,
        models: state.models,
        agents: state.agents,
        chatStyle: state.chatStyle,
        displaySize: state.displaySize,
        isInitializing: false,
      };
    
    default:
      return state;
  }
}

// Context类型定义
interface ChatContextType {
  state: ChatState;
  dispatch: React.Dispatch<ChatAction>;
}

// 创建Context
const ChatContext = createContext<ChatContextType | undefined>(undefined);

// Provider组件
interface ChatProviderProps {
  children: ReactNode;
}

export function ChatProvider({ children }: ChatProviderProps) {
  const [state, dispatch] = useReducer(chatReducer, initialState);

  return (
    <ChatContext.Provider value={{ state, dispatch }}>
      {children}
    </ChatContext.Provider>
  );
}

// Hook for using the context
export function useChatContext() {
  const context = useContext(ChatContext);
  if (context === undefined) {
    throw new Error('useChatContext must be used within a ChatProvider');
  }
  return context;
}

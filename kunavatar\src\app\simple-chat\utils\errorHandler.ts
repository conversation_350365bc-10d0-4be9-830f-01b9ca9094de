import { NextResponse } from 'next/server';
import { z } from 'zod';

/**
 * 统一错误处理工具类
 */
export class ErrorHandler {
  /**
   * 处理API错误并返回标准响应
   */
  static handleApiError(error: unknown): NextResponse {
    console.error('API错误:', error);
    
    // Zod验证错误
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: '输入数据验证失败',
        details: error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message,
        })),
      }, { status: 400 });
    }
    
    // 网络错误
    if (error instanceof TypeError && error.message.includes('fetch')) {
      return NextResponse.json({
        success: false,
        error: '网络连接失败',
        message: '请检查网络连接后重试',
      }, { status: 503 });
    }
    
    // 权限错误
    if (error instanceof Error && error.message.includes('unauthorized')) {
      return NextResponse.json({
        success: false,
        error: '权限不足',
        message: '请重新登录后重试',
      }, { status: 401 });
    }
    
    // 数据库错误
    if (error instanceof Error && (
      error.message.includes('database') || 
      error.message.includes('SQLITE') ||
      error.message.includes('SQL')
    )) {
      return NextResponse.json({
        success: false,
        error: '数据库操作失败',
        message: '服务器内部错误，请稍后重试',
      }, { status: 500 });
    }
    
    // 工具调用错误
    if (error instanceof Error && error.message.includes('tool')) {
      return NextResponse.json({
        success: false,
        error: '工具调用失败',
        message: error.message,
      }, { status: 422 });
    }
    
    // 模型错误
    if (error instanceof Error && (
      error.message.includes('model') ||
      error.message.includes('ollama')
    )) {
      return NextResponse.json({
        success: false,
        error: '模型服务错误',
        message: '模型服务暂时不可用，请稍后重试',
      }, { status: 502 });
    }
    
    // 通用错误
    if (error instanceof Error) {
      return NextResponse.json({
        success: false,
        error: '操作失败',
        message: error.message,
      }, { status: 500 });
    }
    
    // 未知错误
    return NextResponse.json({
      success: false,
      error: '未知错误',
      message: '服务器内部错误',
    }, { status: 500 });
  }

  /**
   * 处理流式错误
   */
  static handleStreamError(
    error: unknown,
    controller: ReadableStreamDefaultController,
    encoder: TextEncoder
  ): void {
    console.error('流式处理错误:', error);
    
    let errorMessage = '处理失败';
    let errorType = 'unknown';
    
    if (error instanceof Error) {
      errorMessage = error.message;
      
      if (error.message.includes('tool')) {
        errorType = 'tool_error';
      } else if (error.message.includes('model')) {
        errorType = 'model_error';
      } else if (error.message.includes('network')) {
        errorType = 'network_error';
      } else if (error.message.includes('abort')) {
        errorType = 'abort_error';
      }
    }
    
    const errorData = {
      type: 'error',
      errorType,
      error: errorMessage,
      timestamp: Date.now()
    };
    
    try {
      controller.enqueue(encoder.encode(`data: ${JSON.stringify(errorData)}\n\n`));
      controller.close();
    } catch (closeError) {
      console.error('关闭流控制器时出错:', closeError);
    }
  }

  /**
   * 验证工具调用错误
   */
  static isToolsNotSupportedError(error: any): boolean {
    if (!error || typeof error.message !== 'string') {
      return false;
    }
    
    const message = error.message.toLowerCase();
    return message.includes('tool') || 
           message.includes('function') ||
           message.includes('schema') ||
           message.includes('not supported');
  }

  /**
   * 创建用户友好的错误消息
   */
  static createUserFriendlyMessage(error: unknown): string {
    if (error instanceof Error) {
      const message = error.message.toLowerCase();
      
      if (message.includes('network') || message.includes('fetch')) {
        return '网络连接失败，请检查网络后重试';
      }
      
      if (message.includes('unauthorized') || message.includes('permission')) {
        return '权限不足，请重新登录';
      }
      
      if (message.includes('model') || message.includes('ollama')) {
        return 'AI模型服务暂时不可用，请稍后重试';
      }
      
      if (message.includes('tool')) {
        return '工具调用失败，请重试或联系管理员';
      }
      
      if (message.includes('database') || message.includes('sql')) {
        return '数据保存失败，请重试';
      }
      
      if (message.includes('validation')) {
        return '输入数据格式错误，请检查后重试';
      }
      
      return error.message;
    }
    
    return '操作失败，请重试';
  }

  /**
   * 记录错误日志
   */
  static logError(
    error: unknown,
    context: string,
    additionalInfo?: Record<string, any>
  ): void {
    const timestamp = new Date().toISOString();
    const errorInfo = {
      timestamp,
      context,
      error: error instanceof Error ? {
        name: error.name,
        message: error.message,
        stack: error.stack
      } : error,
      additionalInfo
    };
    
    console.error(`[${timestamp}] ${context}:`, errorInfo);
  }

  /**
   * 检查是否为可重试的错误
   */
  static isRetryableError(error: unknown): boolean {
    if (!(error instanceof Error)) {
      return false;
    }
    
    const message = error.message.toLowerCase();
    
    // 网络错误通常可以重试
    if (message.includes('network') || message.includes('timeout')) {
      return true;
    }
    
    // 服务器错误可以重试
    if (message.includes('server error') || message.includes('503') || message.includes('502')) {
      return true;
    }
    
    // 工具调用错误可以重试（不使用工具）
    if (message.includes('tool') && !message.includes('not found')) {
      return true;
    }
    
    return false;
  }

  /**
   * 获取错误的HTTP状态码
   */
  static getHttpStatusCode(error: unknown): number {
    if (!(error instanceof Error)) {
      return 500;
    }
    
    const message = error.message.toLowerCase();
    
    if (message.includes('unauthorized') || message.includes('permission')) {
      return 401;
    }
    
    if (message.includes('not found')) {
      return 404;
    }
    
    if (message.includes('validation') || message.includes('invalid')) {
      return 400;
    }
    
    if (message.includes('tool') || message.includes('unprocessable')) {
      return 422;
    }
    
    if (message.includes('network') || message.includes('service unavailable')) {
      return 503;
    }
    
    if (message.includes('model') || message.includes('bad gateway')) {
      return 502;
    }
    
    return 500;
  }
}

'use client';

import React, { useState, useCallback, useRef, useEffect } from 'react';
import {
  useConversationManager,
  useChatMessages,
  useChatStyle,
  useUrlHandler,
  useMessageLoader,
  useConversationEventHandlers,
} from '../hooks';
import { usePageInitializer } from '../hooks/usePageInitializer';
import { Sidebar } from '../../Sidebar';
import { ChatContainer } from './index';
import { streamingChatService } from '../services/streamingChatService';
import { AgentWithRelations } from '../../agents/types';
import { PageLoading } from '../../../components/Loading';
import { useChatPageLogic } from '../hooks/useChatPageLogic';
import { useChatEventHandlers } from '../hooks/useChatEventHandlers';
import { useChatStreamHandlers } from '../hooks/useChatStreamHandlers';
import { ApiClient } from '../utils/apiClient';

type SelectorMode = 'model' | 'agent';

export function ChatPageContainer() {
  const {
    conversations,
    currentConversation,
    loading: conversationLoading,
    error: conversationError,
    hasLoadedConversations,
    loadConversations,
    loadConversationsIfNeeded,
    createConversation,
    switchConversation,
    deleteConversation,
    updateConversationTitle,
  } = useConversationManager();

  const {
    messages,
    setMessages,
    inputMessage,
    setInputMessage,
    isStreaming,
    setIsStreaming,
    selectedModel,
    setSelectedModel,
    models,
    expandedThinkingMessages,
    enableTools,
    setEnableTools,
    selectedTools,
    setSelectedTools,
    setActiveTool,
    setToolCalls,
    setCurrentAssistantMessageId,
    systemPrompt,
    selectAgent,
    toggleThinkingExpand,
    stopGeneration,
    selectBestModel,
    setAbortController,
  } = useChatMessages();

  const { chatStyle, displaySize, setChatStyle: handleChatStyleChange, setDisplaySize: handleDisplaySizeChange } = useChatStyle();

  // UI状态
  const [error, setError] = useState<string | null>(null);
  const [isInitializing, setIsInitializing] = useState(true);
  
  // Agent related state
  const [agents, setAgents] = useState<AgentWithRelations[]>([]);
  const [selectedAgentId, setSelectedAgentId] = useState<number | null>(null);
  const [selectorMode, setSelectorMode] = useState<SelectorMode>('model');
  
  // 记忆面板状态
  const [isMemoryVisible, setIsMemoryVisible] = useState(false);
  
  // 记忆面板切换函数
  const handleMemoryToggle = useCallback(() => {
    setIsMemoryVisible(prev => !prev);
  }, []);

  // 为组件兼容性生成customModels格式
  const [customModels, setCustomModels] = useState<Array<{
    base_model: string;
    display_name: string;
    family?: string;
  }>>([]);

  // 从CustomModel[]生成customModels显示信息
  useEffect(() => {
    if (models.length > 0) {
      const formattedCustomModels = models.map(model => ({
        base_model: model.base_model,
        display_name: model.display_name,
        family: model.family,
      }));
      setCustomModels(formattedCustomModels);
      console.log('✅ 生成customModels显示信息:', formattedCustomModels.length, '个模型');
    }
  }, [models]);

  // 使用自定义hooks
  const chatPageLogic = useChatPageLogic({
    models,
    selectedModel,
    currentConversation,
    conversationLoading,
    createConversation,
    switchConversation,
    setSelectedModel,
    setMessages,
    setToolCalls,
    selectBestModel,
  });

  const chatEventHandlers = useChatEventHandlers({
    currentConversation,
    conversations,
    selectedModel,
    createConversation,
    switchConversation,
    deleteConversation,
    loadConversations,
    setMessages,
    setToolCalls,
    setSelectedModel,
    setError,
  });

  const chatStreamHandlers = useChatStreamHandlers({
    currentConversation,
    updateConversationTitle,
    setMessages,
    setIsStreaming,
    setActiveTool,
    setToolCalls,
    setCurrentAssistantMessageId,
    setError,
    loadConversations,
  });

  // 使用ref来获取最新的messages值，避免在useCallback依赖中包含messages
  const messagesRef = useRef(messages);
  const selectedModelRef = useRef(selectedModel);
  const setMessagesRef = useRef(setMessages);
  const setActiveToolRef = useRef(setActiveTool);
  const setToolCallsRef = useRef(setToolCalls);
  const setCurrentAssistantMessageIdRef = useRef(setCurrentAssistantMessageId);
  const setIsStreamingRef = useRef(setIsStreaming);
  const setErrorRef = useRef(setError);
  const loadConversationsRef = useRef(loadConversations);
  const systemPromptRef = useRef(systemPrompt);
  const cleanupHandlersRef = useRef<Array<() => void>>([]);

  useEffect(() => {
    messagesRef.current = messages;
    selectedModelRef.current = selectedModel;
    setMessagesRef.current = setMessages;
    setActiveToolRef.current = setActiveTool;
    setToolCallsRef.current = setToolCalls;
    setCurrentAssistantMessageIdRef.current = setCurrentAssistantMessageId;
    setIsStreamingRef.current = setIsStreaming;
    setErrorRef.current = setError;
    loadConversationsRef.current = loadConversations;
    systemPromptRef.current = systemPrompt;
  }, [messages, selectedModel, loadConversations, systemPrompt]);

  // 🔧 修复：组件卸载时清理所有pending的更新
  useEffect(() => {
    return () => {
      cleanupHandlersRef.current.forEach(cleanup => cleanup());
      cleanupHandlersRef.current = [];
    };
  }, []);

  // 管理初始化状态，平衡加载体验和防闪屏
  useEffect(() => {
    if (models.length > 0) {
      const timer = setTimeout(() => {
        setIsInitializing(false);
      }, 200);
      
      return () => clearTimeout(timer);
    } else {
      const timeout = setTimeout(() => {
        setIsInitializing(false);
      }, 2000);
      
      return () => clearTimeout(timeout);
    }
  }, [models.length]);

  // 处理模型切换，传递对话ID以保存对话特定的模型选择
  const handleModelChange = (modelName: string) => {
    const conversationId = currentConversation?.id;
    setSelectedModel(modelName, conversationId);
  };

  // Fetch agents - 优化：添加缓存和错误处理
  useEffect(() => {
    let isMounted = true;

    const fetchAgents = async () => {
      try {
        console.log('🤖 开始加载智能体列表');
        const agents: AgentWithRelations[] = await ApiClient.getAgents();
        if (isMounted) {
          setAgents(agents);
          console.log(`✅ 成功加载 ${agents.length} 个智能体`);
        }
      } catch (error) {
        if (isMounted) {
          console.error('❌ 加载智能体失败:', error);
        }
      }
    };

    fetchAgents();

    return () => {
      isMounted = false;
    };
  }, []);

  const handleAgentChange = (agentId: number | null) => {
    setSelectedAgentId(agentId);
    selectAgent(agentId);
  };

  const handleSelectorModeChange = (mode: SelectorMode) => {
    setSelectorMode(mode);
  };

  // 使用 URL 处理器
  useUrlHandler({
    models,
    selectedModel,
    currentConversation,
    conversationLoading,
    conversations,
    createConversation,
    switchConversation,
    setSelectedModel,
  });

  // 使用消息加载器
  useMessageLoader({
    currentConversation,
    setSelectedModel,
    setMessages,
    setToolCalls,
    selectedModel,
    models,
    selectBestModel,
  });

  // 使用对话事件处理器
  useConversationEventHandlers({
    currentConversation,
    conversations,
    selectedModel,
    createConversation,
    switchConversation,
    deleteConversation,
    loadConversations,
    setMessages,
    setToolCalls,
    setSelectedModel,
    setError,
    setIsProcessingUrl: () => {}, // 暂时提供一个空函数
  });

  // 发送消息的核心逻辑
  const sendMessage = useCallback(async () => {
    if (!inputMessage.trim() || !selectedModel || isStreaming) {
      return;
    }

    let activeConversation = currentConversation;
    if (!activeConversation) {
      const title = inputMessage.trim().substring(0, 30) + (inputMessage.length > 30 ? '...' : '');
      const conversationId = await createConversation(title, selectedModel);
      if (!conversationId) {
        setError('创建对话失败');
        return;
      }
      await new Promise(resolve => setTimeout(resolve, 100));
      activeConversation = currentConversation;
    }

    const userMessage = {
      id: `user-${Date.now()}`,
      role: 'user' as const,
      content: inputMessage.trim(),
      timestamp: Date.now(),
    };

    // 获取当前的消息列表（使用ref避免在依赖中包含messages）
    const currentMessages = messagesRef.current;

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsStreaming(true);
    setError(null);
    setToolCalls([]);
    setActiveTool(null);

    const assistantMessageId = `assistant-${Date.now()}`;
    const assistantMessage = {
      id: assistantMessageId,
      role: 'assistant' as const,
      content: '',
      timestamp: Date.now(),
      model: selectedModel,
    };

    setMessages(prev => [...prev, assistantMessage]);
    setCurrentAssistantMessageId(assistantMessageId);

    try {
      // 创建新的 AbortController
      const controller = new AbortController();
      setAbortController(controller);

      // 获取标题总结设置
      let titleSummarySettings = { enabled: false, model: '' };
      try {
        const savedSettings = localStorage.getItem('prompt_optimize_settings');
        if (savedSettings) {
          const settings = JSON.parse(savedSettings);
          titleSummarySettings = {
            enabled: settings.titleSummaryEnabled || false,
            model: settings.titleSummaryModel || ''
          };
        }
      } catch (error) {
        console.error('Failed to load title summary settings:', error);
      }

      const chatRequestBody = {
        model: selectedModel,
        conversationId: activeConversation?.id,
        agentId: selectedAgentId,
        messages: [
          ...(systemPromptRef.current ? [{ role: 'system', content: systemPromptRef.current }] : []),
          ...currentMessages.map(msg => ({
            role: msg.role,
            content: msg.content,
          })),
          {
            role: 'user',
            content: userMessage.content,
          },
        ],
        stream: true,
        enableTools,
        selectedTools,
        titleSummarySettings,
      };

      const response = await ApiClient.sendChatMessage(chatRequestBody);

      if (!response.ok) {
        throw new Error('聊天请求失败');
      }

      // 使用流式服务处理响应，传递 AbortController
      await streamingChatService.processStreamingResponse(response, chatStreamHandlers.createStreamHandlers(), assistantMessageId, controller);

    } catch (err) {
      // 如果是中断错误，不显示错误信息
      if (err instanceof Error && err.name === 'AbortError') {
        console.log('🛑 用户主动停止了生成');
      } else {
        setError(err instanceof Error ? err.message : '发送消息失败');
        setMessages(prev => prev.filter(msg => msg.id !== assistantMessageId));
      }
    } finally {
      setIsStreaming(false);
      setAbortController(null);
    }
  }, [
    inputMessage, selectedModel, isStreaming, currentConversation,
    enableTools, selectedTools, createConversation, setAbortController,
    chatStreamHandlers, setActiveTool, setCurrentAssistantMessageId, setInputMessage,
    setIsStreaming, setMessages, setToolCalls, selectedAgentId, systemPromptRef,
    messagesRef, setError
  ]);

  // 插入文本到输入框
  const handleInsertText = useCallback((text: string) => {
    setInputMessage(text);
  }, [setInputMessage]);

  // 确保侧边栏有对话列表数据 - 立即加载，不延迟
  useEffect(() => {
    // 立即加载对话列表，确保URL处理器能获取到正确的对话数据
    loadConversationsIfNeeded();
  }, [loadConversationsIfNeeded]);

  return (
    <div className="flex h-screen bg-theme-background-secondary dark:bg-theme-background overflow-hidden">
      {/* 侧边栏 */}
      <Sidebar
        conversations={conversations}
        currentConversation={currentConversation}
        onCreateConversation={chatEventHandlers.handleCreateConversation}
        onLoadConversation={chatEventHandlers.handleLoadConversation}
        onDeleteConversation={chatEventHandlers.handleDeleteConversation}
      />

      {/* 主聊天区域 */}
      {isInitializing ? (
        <div className="flex-1 overflow-auto">
          <PageLoading
            text="loading"
            fullScreen={true}
          />
        </div>
      ) : (
        <ChatContainer
          currentConversation={currentConversation}
          models={models}
          selectedModel={selectedModel}
          onModelChange={handleModelChange}
          agents={agents}
          selectedAgentId={selectedAgentId}
          onAgentChange={handleAgentChange}
          selectorMode={selectorMode}
          onSelectorModeChange={handleSelectorModeChange}
          customModels={customModels}
          messages={messages}
          inputMessage={inputMessage}
          onInputChange={setInputMessage}
          onSendMessage={sendMessage}
          isStreaming={isStreaming}
          onStopGeneration={stopGeneration}
          expandedThinkingMessages={expandedThinkingMessages}
          onToggleThinkingExpand={toggleThinkingExpand}
          enableTools={enableTools}
          selectedTools={selectedTools}
          onToolsToggle={setEnableTools}
          onSelectedToolsChange={setSelectedTools}
          onInsertText={handleInsertText}
          onClearChat={chatEventHandlers.clearCurrentChat}
          error={error}
          onDismissError={() => setError(null)}
          chatStyle={chatStyle}
          displaySize={displaySize}
          onChatStyleChange={handleChatStyleChange}
          onDisplaySizeChange={handleDisplaySizeChange}
          isMemoryVisible={isMemoryVisible}
          onMemoryToggle={handleMemoryToggle}
        />
      )}
    </div>
  );
}

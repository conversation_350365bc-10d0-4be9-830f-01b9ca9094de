'use client';

import React, { useState } from 'react';
import { Pa<PERSON>, MessageSquare, Bo<PERSON>, Check, Monitor, Minimize2 } from 'lucide-react';
import { BaseControlButton } from './BaseControlButton';

export type ChatStyle = 'conversation' | 'bubble';
export type DisplaySize = 'fullscreen' | 'compact';

interface ChatStyleControlProps {
  chatStyle: ChatStyle;
  displaySize?: DisplaySize;
  onStyleChange: (style: ChatStyle) => void;
  onDisplaySizeChange?: (size: DisplaySize) => void;
}

export function ChatStyleControl({ 
  chatStyle, 
  displaySize = 'fullscreen',
  onStyleChange, 
  onDisplaySizeChange 
}: ChatStyleControlProps) {
  const [showMenu, setShowMenu] = useState(false);

  const handleStyleSelect = (style: ChatStyle) => {
    onStyleChange(style);
    setShowMenu(false);
  };

  const handleDisplaySizeSelect = (size: DisplaySize) => {
    if (onDisplaySizeChange) {
      onDisplaySizeChange(size);
    }
    setShowMenu(false);
  };

  return (
    <div className="relative">
      <BaseControlButton
        onClick={() => setShowMenu(!showMenu)}
        tooltip="聊天样式设置"
        variant="default"
      >
        <Palette className="w-5 h-5" />
      </BaseControlButton>

      {/* 样式选择菜单 */}
      {showMenu && (
        <>
          {/* 背景点击层 */}
          <div 
            className="fixed inset-0 z-30 pointer-events-auto"
            onClick={() => setShowMenu(false)}
            style={{ background: 'transparent' }}
          />
          
          {/* 菜单内容 */}
          <div className="absolute bottom-full left-0 mb-2 z-40 bg-[var(--color-card)] border border-[var(--color-border)] rounded-[var(--radius-lg)] w-48 animate-scale-up">
            {/* 对话样式标题 */}
            <div className="px-3 py-2 border-b border-[var(--color-border)]">
              <span className="text-sm font-medium text-[var(--color-foreground)]">对话样式</span>
            </div>
            
            {/* 对话样式选项 */}
            <div className="py-1">
              {/* 对话模式 */}
              <button
                onClick={() => handleStyleSelect('conversation')}
                className="w-full px-3 py-2 text-left hover:bg-[var(--color-card-hover)] transition-colors flex items-center justify-between group"
              >
                <div className="flex items-center gap-2">
                  <MessageSquare className="w-4 h-4 text-[var(--color-foreground-muted)]" />
                  <span className="text-sm text-[var(--color-foreground)]">对话模式</span>
                </div>
                {chatStyle === 'conversation' && (
                  <Check className="w-4 h-4 text-[var(--color-primary)]" />
                )}
              </button>
              
              {/* 气泡模式 */}
              <button
                onClick={() => handleStyleSelect('bubble')}
                className="w-full px-3 py-2 text-left hover:bg-[var(--color-card-hover)] transition-colors flex items-center justify-between group"
              >
                <div className="flex items-center gap-2">
                  <Bot className="w-4 h-4 text-[var(--color-foreground-muted)]" />
                  <span className="text-sm text-[var(--color-foreground)]">气泡模式</span>
                </div>
                {chatStyle === 'bubble' && (
                  <Check className="w-4 h-4 text-[var(--color-primary)]" />
                )}
              </button>
            </div>

            {/* 显示尺寸标题 */}
            {onDisplaySizeChange && (
              <>
                <div className="px-3 py-2 border-b border-[var(--color-border)] border-t border-[var(--color-border)]">
                  <span className="text-sm font-medium text-[var(--color-foreground)]">显示尺寸</span>
                </div>
                
                {/* 显示尺寸选项 */}
                <div className="py-1">
                  {/* 全屏模式 */}
                  <button
                    onClick={() => handleDisplaySizeSelect('fullscreen')}
                    className="w-full px-3 py-2 text-left hover:bg-[var(--color-card-hover)] transition-colors flex items-center justify-between group"
                  >
                    <div className="flex items-center gap-2">
                      <Monitor className="w-4 h-4 text-[var(--color-foreground-muted)]" />
                      <span className="text-sm text-[var(--color-foreground)]">全屏模式</span>
                    </div>
                    {displaySize === 'fullscreen' && (
                      <Check className="w-4 h-4 text-[var(--color-primary)]" />
                    )}
                  </button>
                  
                  {/* 紧凑模式 */}
                  <button
                    onClick={() => handleDisplaySizeSelect('compact')}
                    className="w-full px-3 py-2 text-left hover:bg-[var(--color-card-hover)] transition-colors flex items-center justify-between group"
                  >
                    <div className="flex items-center gap-2">
                      <Minimize2 className="w-4 h-4 text-[var(--color-foreground-muted)]" />
                      <span className="text-sm text-[var(--color-foreground)]">紧凑模式</span>
                    </div>
                    {displaySize === 'compact' && (
                      <Check className="w-4 h-4 text-[var(--color-primary)]" />
                    )}
                  </button>
                </div>
              </>
            )}
          </div>
        </>
      )}
    </div>
  );
}
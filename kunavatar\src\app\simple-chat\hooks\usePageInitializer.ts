'use client';

import { useEffect, useRef, useState } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { Conversation } from '@/lib/database/types';

interface UsePageInitializerProps {
  conversations: Conversation[];
  currentConversation: any;
  conversationLoading: boolean;
  hasLoadedConversations: boolean;
}

export function usePageInitializer({
  conversations,
  currentConversation,
  conversationLoading,
  hasLoadedConversations,
}: UsePageInitializerProps) {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [hasInitialized, setHasInitialized] = useState(false);
  
  useEffect(() => {
    // 只在页面首次加载且没有URL参数时执行初始化
    if (hasInitialized || conversationLoading || !hasLoadedConversations) {
      return;
    }
    
    const shouldCreateNew = searchParams.get('new') === 'true';
    const conversationId = searchParams.get('id');
    
    // 如果有明确的URL参数，不需要初始化处理
    if (shouldCreateNew || conversationId) {
      setHasInitialized(true);
      return;
    }
    
    // 如果已经有当前对话，不需要处理
    if (currentConversation) {
      setHasInitialized(true);
      return;
    }
    
    // 处理默认行为：有对话历史则进入最新对话，无对话历史则创建新对话
    if (conversations.length > 0) {
      // 有对话历史，重定向到最新对话
      const latestConversation = conversations[0];
      console.log(`🏠 页面初始化 - 重定向到最新对话 ${latestConversation.id}`);
      router.replace(`/simple-chat?id=${latestConversation.id}`);
    } else {
      // 没有对话历史，重定向到新建对话
      console.log('🏠 页面初始化 - 重定向到新建对话');
      router.replace('/simple-chat?new=true');
    }
    
    setHasInitialized(true);
  }, [
    hasInitialized,
    conversations,
    currentConversation,
    conversationLoading,
    hasLoadedConversations,
    searchParams,
    router,
  ]);
  
  return {
    hasInitialized,
  };
}

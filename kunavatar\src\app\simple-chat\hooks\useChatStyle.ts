import { useState, useEffect } from 'react';
import { ChatStyle, DisplaySize } from '../components/input-controls';

const CHAT_STYLE_KEY = 'simple-chat-style';
const DISPLAY_SIZE_KEY = 'simple-chat-display-size';

export function useChatStyle() {
  const [chatStyle, setChatStyle] = useState<ChatStyle>('conversation');
  const [displaySize, setDisplaySize] = useState<DisplaySize>('fullscreen');
  const [isLoaded, setIsLoaded] = useState(false);

  // 从本地存储加载聊天样式和显示尺寸偏好
  useEffect(() => {
    if (typeof window === 'undefined') {
      setIsLoaded(true);
      return;
    }
    try {
      const savedStyle = localStorage.getItem(CHAT_STYLE_KEY) as ChatStyle;
      if (savedStyle && (savedStyle === 'conversation' || savedStyle === 'bubble')) {
        setChatStyle(savedStyle);
      }

      const savedDisplaySize = localStorage.getItem(DISPLAY_SIZE_KEY) as DisplaySize;
      if (savedDisplaySize && (savedDisplaySize === 'fullscreen' || savedDisplaySize === 'compact')) {
        setDisplaySize(savedDisplaySize);
      }
    } catch (err) {
      console.log('读取聊天偏好失败:', err);
    } finally {
      setIsLoaded(true);
    }
  }, []);

  // 保存聊天样式到本地存储
  const handleChatStyleChange = (style: ChatStyle) => {
    setChatStyle(style);
    if (typeof window === 'undefined') return;
    try {
      localStorage.setItem(CHAT_STYLE_KEY, style);
    } catch (err) {
      console.log('保存聊天样式偏好失败:', err);
    }
  };

  // 保存显示尺寸到本地存储
  const handleDisplaySizeChange = (size: DisplaySize) => {
    setDisplaySize(size);
    if (typeof window === 'undefined') return;
    try {
      localStorage.setItem(DISPLAY_SIZE_KEY, size);
    } catch (err) {
      console.log('保存显示尺寸偏好失败:', err);
    }
  };

  return {
    chatStyle,
    displaySize,
    setChatStyle: handleChatStyleChange,
    setDisplaySize: handleDisplaySizeChange,
    isLoaded,
  };
}